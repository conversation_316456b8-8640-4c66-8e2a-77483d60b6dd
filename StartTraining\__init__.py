import logging
import json
import azure.functions as func
from pydantic import BaseModel
from QnAHandlers.MainHandler import *
from QnAHandlers.ORM import *
from QnAHandlers.QAGenerator import *
import azure.functions as func
import logging
import json

async def main(req: func.HttpRequest) -> func.HttpResponse:
    """Process client data endpoint with flexible processing options"""
    try:
        # Get required parameters
        client_id = req.params.get('clientId')
        if not client_id:
            return func.HttpResponse(
                "Please provide a clientId parameter",
                status_code=400
            )

        # Get optional parameters
        body = req.get_json() if req.get_body() else {}
        
        # Create processing options from request
        options = ProcessingOptions(
            handle_unanswered=body.get('handleUnanswered', True),
            generate_new=body.get('generateNew', True),
            process_embeddings=body.get('processEmbeddings', True),
            full_data=body.get('fullData', False)  
        )
        
        # Get number of QA pairs if specified
        num_qa_pairs = body.get('numQAPairs', Config.NUMBER_OF_QA_PAIRS)
        
        processor = DataProcessor()
        result = await processor.process_client(
            client_id=client_id,
            options=options,
            num_qa_pairs=num_qa_pairs
        )
        
        return func.HttpResponse(
            json.dumps({
                "status": "success",
                "processing_options": {
                    "handleUnanswered": options.handle_unanswered,
                    "generateNew": options.generate_new,
                    "processEmbeddings": options.process_embeddings,
                    "fullData": options.full_data
                },
                "results": {
                    "auto_answered_questions": result["auto_answered_count"],
                    "questions_needing_manual_answer": result["need_manual_answer_count"],
                    "new_qa_pairs_generated": result["new_qa_pairs_count"]
                }
            }),
            mimetype="application/json"
        )
        
    except Exception as e:
        logging.error(f"Error processing client data: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "status": "error",
                "error": str(e)
            }),
            status_code=500,
            mimetype="application/json"
        )
