import json
from openai import OpenAI
#API from test3 metasence account
client = OpenAI(
  api_key ='********************************************************',
)


def generate_and_store_embeddings(input_json, output_json):
    # Load the input JSON
    with open(input_json, 'r') as f:
        data = json.load(f)
    
    # Create a list to store the embeddings for each keyword
    embeddings_data = []

    # Create embeddings for each keyword in the data
    for item in data:
        keywords = item.get("keywords", [])
        subcategory = item.get("subcategory", "")
        if subcategory != "":
            keywords.insert(0, subcategory)

        for keyword in keywords:
            response = client.embeddings.create(
                input=keyword,
                model="text-embedding-ada-002"
            )
            embedding = response.data[0].embedding
            embeddings_data.append({"categoryName": item["subcategory"],"keyword": keyword, "embedding": embedding})
    
    # Write the embeddings data to the output JSON file
    with open(output_json, 'w') as f:
        json.dump(embeddings_data, f, indent=4)
    
    return embeddings_data
# # Example usage
input_json = 'categories.example.json'
output_json = 'categories.json'
updated_data = generate_and_store_embeddings(input_json,output_json)
# print(updated_data)
