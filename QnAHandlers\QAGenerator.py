import json
import logging
from typing import Dict, <PERSON>, Tuple
from openai import AsyncOpenAI
from QnAHandlers.ORM import QnaPage, QnaCatalog, QuestionAnswer
import os
from sqlalchemy.orm import sessionmaker
from sqlalchemy.engine.url import URL
import tiktoken

class Config:
    CONNECTION_STRING = os.environ["QnAStorageAccount"]
    DATABASE_CONNECTION = (
        "Driver={ODBC Driver 17 for SQL Server};"
        "Server=3geniidev.database.windows.net;"
        "Database=QA_Slicky_Admin;"
        "UID=fouzir;"
        "PWD=Changeme78;"
        "Encrypt=yes;"
        "TrustServerCertificate=no;"
    )   
    LLM_MODEL = "gpt-4o-mini"
    BLOB_CONTAINER = "client-data"
    OPENAI_API_KEY = os.environ["OPENAI_API_KEY"]
    FORCE_REPROCESS = os.environ.get("FORCE_REPROCESS", "false").lower() == "true"
    CHUNK_SIZE = 500
    CHUNK_OVERLAP = 50
    NUMBER_OF_QA_PAIRS = 10



class QAGenerator:
    def __init__(self, openai_client: AsyncOpenAI, session_maker: sessionmaker):
        self.openai_client = openai_client
        self.Session = session_maker
        self._current_session = None
        self.tokenizer = tiktoken.get_encoding("cl100k_base")
        self.max_tokens = 100000  # Setting a safe limit below the 128k maximum

    def _count_tokens(self, text: str) -> int:
        """Count tokens in a text string"""
        return len(self.tokenizer.encode(text))

    def _chunk_context(self, context_items: List[Dict], max_tokens: int) -> List[List[Dict]]:
        """Split context items into chunks that fit within token limit"""
        chunks = []
        current_chunk = []
        current_tokens = 0

        for item in context_items:
            # Calculate tokens for this item
            item_text = f"{item['type']} '{item['name']}': {item['content']}"
            item_tokens = self._count_tokens(item_text)

            # If adding this item would exceed the limit, start a new chunk
            if current_tokens + item_tokens > max_tokens and current_chunk:
                chunks.append(current_chunk)
                current_chunk = []
                current_tokens = 0

            # Add item to current chunk
            current_chunk.append(item)
            current_tokens += item_tokens

        # Add the last chunk if it's not empty
        if current_chunk:
            chunks.append(current_chunk)

        return chunks

    def _prepare_context_items(self, context_data: Dict) -> List[Dict]:
        """Prepare context items with type information"""
        context_items = []
        
        # Add pages
        for page in context_data.get("pages", []):
            context_items.append({
                "type": "Page",
                "name": page["name"],
                "content": page["content"]
            })
        
        # Add catalogs
        for catalog in context_data.get("catalogs", []):
            context_items.append({
                "type": catalog["type"].capitalize(),
                "name": catalog["name"],
                "content": catalog["content"]
            })
        
        return context_items

    async def generate_qa_pairs(self, context_data: Dict, client_id: str, num_pairs: int = 5) -> List[Dict]:
        """Generate question-answer pairs from context data with chunking support"""
        try:
            # Prepare context items
            context_items = self._prepare_context_items(context_data)
            
            # Split into chunks
            context_chunks = self._chunk_context(context_items, self.max_tokens)
            
            all_qa_pairs = []
            pairs_per_chunk = max(1, num_pairs // len(context_chunks))
            remaining_pairs = num_pairs % len(context_chunks)

            for i, chunk in enumerate(context_chunks):
                # Calculate how many pairs to generate from this chunk
                chunk_pairs = pairs_per_chunk + (1 if i < remaining_pairs else 0)
                if chunk_pairs == 0:
                    continue

                try:
                    # Prepare context string for this chunk
                    context = "\n\n".join([
                        f"{item['type']} '{item['name']}': {item['content']}"
                        for item in chunk
                    ])

                    messages = [
                        {
                            "role": "system",
                            "content": f"""Based on the provided context, generate {chunk_pairs} diverse question-answer pairs. 
                            The questions should be relevant to the content and cover different aspects of the information provided.
                            
                            Return the response as a JSON object with this structure:
                            {{
                                "qa_pairs": [
                                    {{
                                        "question": "The generated question",
                                        "answer": "The comprehensive answer",
                                        "questionSource": 1,
                                        "answerSource": 1
                                    }}
                                ]
                            }}"""
                        },
                        {
                            "role": "user",
                            "content": f"Context:\n{'-' * 80}\n{context}\n\nGenerate {chunk_pairs} QA pairs in JSON format:"
                        }
                    ]
                                    # {{
                                    #     "question": "The generated question",
                                    #     "answer": "The comprehensive answer",
                                    #     "questionSource": "Source of the question (e.g., 'Page: About Us')",
                                    #     "answerSource": "Source of the answer information"
                                    # }}

                    completion = await self.openai_client.chat.completions.create(
                        model=Config.LLM_MODEL,
                        messages=messages,
                        temperature=0.7,
                        response_format={"type": "json_object"}
                    )

                    chunk_qa_pairs = json.loads(completion.choices[0].message.content).get("qa_pairs", [])
                    all_qa_pairs.extend(chunk_qa_pairs)

                except Exception as e:
                    logging.error(f"Error generating QA pairs for chunk {i}: {str(e)}")
                    continue

            # Add metadata to all generated pairs
            for qa in all_qa_pairs:
                qa["clientId"] = client_id
                qa["processed"] = False
                qa["requestManualAnswer"] = False

            return all_qa_pairs

        except Exception as e:
            logging.error(f"Error generating QA pairs: {str(e)}")
            raise

    async def handle_unanswered_questions(self, client_id: str) -> Tuple[List[Dict], List[Dict]]:
        """Process unanswered questions and determine which need manual answers"""
        session = self.Session()
        try:
            # Get questions with empty answers
            unanswered_questions = session.query(QuestionAnswer).filter(
                QuestionAnswer.clientId == client_id,
                QuestionAnswer.answer.is_(None) | (QuestionAnswer.answer == '')
            ).all()

            if not unanswered_questions:
                return [], []

            # Prepare context data
            context_data = await self._get_context_data(client_id, session)
            context_items = self._prepare_context_items(context_data)
            
            # Split context into chunks
            context_chunks = self._chunk_context(context_items, self.max_tokens)

            auto_answered = []
            need_manual_answer = []

            for question in unanswered_questions:
                answer_found = False
                
                # Try each context chunk until we find an answer
                for chunk in context_chunks:
                    try:
                        result = await self._process_question_with_context(question.question, chunk)

                        if result.get("can_answer", False):
                            question.answer = result["answer"]
                            question.answerSource = result["answerSource"]
                            auto_answered.append(question)
                            answer_found = True
                            break

                    except Exception as e:
                        logging.error(f"Error processing chunk for question {question.id}: {str(e)}")
                        continue

                if not answer_found:
                    question.requestManualAnswer = True
                    need_manual_answer.append(question)

            session.commit()
            return auto_answered, need_manual_answer

        except Exception as e:
            session.rollback()
            logging.error(f"Error handling unanswered questions: {str(e)}")
            raise
        finally:
            session.close()

    async def _process_question_with_context(self, question: str, context_chunk: List[Dict]) -> Dict:
        """Process a single question with a context chunk"""
        context = "\n\n".join([
            f"{item['type']} '{item['name']}': {item['content']}"
            for item in context_chunk
        ])

        messages = [
            {
                "role": "system",
                "content": """Based on the provided context, answer the question.
                If you can find relevant information in the context, provide the answer and source.
                If you cannot find relevant information, respond with {"can_answer": false}.
                
                For answerable questions, return:
                {
                    "can_answer": true,
                    "answer": "The comprehensive answer",
                    "answerSource": 1
                }"""
            },
            {
                "role": "user",
                "content": f"Context:\n{'-' * 80}\n{context}\n\nQuestion: {question}\n\nAnswer in JSON format:"
            }
        ]

        completion = await self.openai_client.chat.completions.create(
            model=Config.LLM_MODEL,
            messages=messages,
            temperature=0,
            response_format={"type": "json_object"}
        )

        return json.loads(completion.choices[0].message.content)

    async def _get_context_data(self, client_id: str, session) -> Dict:
        """Get context data from database"""
        pages = session.query(QnaPage).filter(QnaPage.clientId == client_id).all()
        catalogs = session.query(QnaCatalog).filter(QnaCatalog.clientId == client_id).all()
        
        pages_dict = [{k: v for k, v in row.__dict__.items() if not k.startswith('_')} 
                     for row in pages]
        catalogs_dict = [{k: v for k, v in row.__dict__.items() if not k.startswith('_')} 
                        for row in catalogs]
        
        return {
            "pages": pages_dict,
            "catalogs": catalogs_dict
        }
