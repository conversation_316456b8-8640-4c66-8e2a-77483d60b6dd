import azure.functions as func
import json
import logging
from openai import OpenAI

# Initialize the OpenAI client with your organization and API key as before
client = OpenAI(
  
  api_key ='********************************************************',
)

def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Python HTTP trigger function processed a request to create blog content.')

    try:
        # Get the subject from the query parameter
        subject = req.params.get('subject')
        
        if not subject:
            return func.HttpResponse(json.dumps({'error': 'Subject parameter is missing.'}), mimetype="application/json", status_code=400)

        # Generate content based on the subject
        content_response = generate_blog_content(subject)

        if content_response:
            return func.HttpResponse(json.dumps(content_response), mimetype="application/json", status_code=200)
        else:
            return func.HttpResponse(json.dumps({'error': 'Failed to generate blog content.'}), mimetype="application/json", status_code=500)
    except Exception as e:
        logging.error(f"Exception occurred: {e}")
        return func.HttpResponse(json.dumps({'error': str(e)}), mimetype="application/json", status_code=500)

def generate_blog_content(subject):
    iter = 0
    while (iter < 3):
        try:
            messages = [{"role":"user","content":"""
    Create a newsletter or blog content about Subject :"""+subject+""". Include 5-7 paragraphs, each with a unique title related to the subject, keywords for an image, and HTML-formatted text. Ensure the content is informative and engaging.
    in a structured json like this teamplate : 
{
	paragraphs: [
		{
			title: "title 1 about the subject"
			image: "image keywords comma separated",
			text: "HTML Block content should be 1000 caracters long: <p>...</p>"
		},
		{
			title: "title 2 about the subject"
			image: "image 2 keywords comma separated",
			text: "HTML Block content should be 1000 caracters long: <p>...</p>"
		},
		...
	]	
}
Note: each text should be minimum 1000 caracter long 
Json Output :
    """ }]
            result = client.chat.completions.create(
                model="gpt-4.1-mini",
                messages=messages,
                response_format={ "type": "json_object" }
            )
            alternatives = json.loads(result.choices[0].message.content)
            alternatives["subject"] = subject
            return alternatives
        except:
            iter +=1
            continue    
    return None
