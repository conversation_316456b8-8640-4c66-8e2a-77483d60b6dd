import azure.functions as func
import logging
import json
import pyodbc
import asyncio
from azure.storage.blob import BlobServiceClient
from typing import List, Dict, Optional, Tuple
import os
from datetime import datetime
import hashlib
from dataclasses import dataclass
import numpy as np
from openai import Async<PERSON>penAI
from concurrent.futures import ThreadPoolExecutor
import uuid
import tiktoken
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.engine.url import URL
from QnAHandlers.QAGenerator import QAGenerator
from QnAHandlers.ORM import *


# Configuration
class Config:
    CONNECTION_STRING = os.environ["QnAStorageAccount"]
    DATABASE_CONNECTION = (
        "Driver={ODBC Driver 17 for SQL Server};"
        "Server=3geniidev.database.windows.net;"
        "Database=QA_Slicky_Admin;"
        "UID=fouzir;"
        "PWD=Changeme78;"
        "Encrypt=yes;"
        "TrustServerCertificate=no;"
    )   
    LLM_MODEL = "gpt-4o-mini"
    BLOB_CONTAINER = "client-data"
    OPENAI_API_KEY = os.environ["OPENAI_API_KEY"]
    FORCE_REPROCESS = os.environ.get("FORCE_REPROCESS", "false").lower() == "true"
    CHUNK_SIZE = 500
    CHUNK_OVERLAP = 50
    NUMBER_OF_QA_PAIRS = 10


@dataclass
class ProcessingOptions:
    handle_unanswered: bool = False  # Process existing unanswered questions
    generate_new: bool = False       # Generate new QA pairs
    process_embeddings: bool = False # Process and store embeddings
    full_data: bool = False           # If True, fetch all data; if False, only unprocessed data

class DateTimeEncoder(json.JSONEncoder):
    """Custom JSON encoder for datetime and UUID objects"""
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        if isinstance(obj, uuid.UUID):
            return str(obj)
        return super().default(obj)

class CustomJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder for handling UUID, datetime, and other special types"""
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        if isinstance(obj, uuid.UUID):
            return str(obj)
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj)
        return super().default(obj)

class EmbeddingsProcessor:
    def __init__(self, openai_client: AsyncOpenAI, blob_service: BlobServiceClient):
        self.openai_client = openai_client
        self.blob_service = blob_service
        self.container = blob_service.get_container_client(Config.BLOB_CONTAINER)
        self.tokenizer = tiktoken.get_encoding("cl100k_base")
        self.json_encoder = CustomJSONEncoder


    async def get_similar_content(self, question: str, client_id: str, top_k: int = 3) -> List[Dict]:
        try:
            question_embedding = await self.get_embedding(question)

            blob_name = f"{client_id}/embeddings.json"
            blob_client = self.container.get_blob_client(blob_name)
            
            downloaded_blob = blob_client.download_blob()
            content = downloaded_blob.readall()
            stored_embeddings = json.loads(content)

            all_similarities = []

            for category, items in stored_embeddings.items():
                for item in items:
                    similarity = np.dot(question_embedding, item["embedding"]) / (
                        np.linalg.norm(question_embedding) * np.linalg.norm(item["embedding"])
                    )
                    all_similarities.append({
                        "id": item["id"],
                        "type": category,
                        "similarity": float(similarity),
                        "metadata": item.get("metadata", {})
                    })

            return sorted(all_similarities, key=lambda x: x["similarity"], reverse=True)[:top_k]

        except Exception as e:
            logging.error(f"Error getting similar content: {str(e)}")
            return []


    def chunk_text(self, text: str, chunk_size: int = 8000, overlap: int = 100) -> List[str]:
        """Split text into chunks that won't exceed token limit"""
        tokens = self.tokenizer.encode(text)
        chunks = []
        
        for i in range(0, len(tokens), chunk_size - overlap):
            chunk_tokens = tokens[i:i + chunk_size]
            chunk_text = self.tokenizer.decode(chunk_tokens)
            chunks.append(chunk_text)
            
            if i + chunk_size >= len(tokens):
                break
        
        return chunks

    async def get_embedding(self, text: str) -> List[float]:
        """Get embedding for text using OpenAI with chunking support"""
        try:
            # Check if text needs chunking
            token_count = len(self.tokenizer.encode(text))
            if token_count > 8000:  # Leave some margin below the 8192 limit
                chunks = self.chunk_text(text)
                # Get embeddings for all chunks
                embeddings = []
                for chunk in chunks:
                    response = await self.openai_client.embeddings.create(
                        input=chunk,
                        model="text-embedding-3-small"
                    )
                    embeddings.append(response.data[0].embedding)
                # Average the embeddings
                return [sum(x)/len(x) for x in zip(*embeddings)]
            else:
                response = await self.openai_client.embeddings.create(
                    input=text,
                    model="text-embedding-3-small"
                )
                return response.data[0].embedding
        except Exception as e:
            logging.error(f"Error generating embedding: {str(e)}")
            raise

    async def process_and_store_embeddings(self, client_id: str, data: Dict):
        try:
            embeddings_data = {
                "pages": [],
                "catalogs": [],
                "qa_pairs": []
            }

            for page in data.get("pages", []):
                text = f"{page['name']}\n{page['content']}"
                embedding = await self.get_embedding(text)
                embeddings_data["pages"].append({
                    "id": page["id"],
                    "embedding": embedding,
                    "metadata": {"type": "page", "name": page["name"]}
                })

            for catalog in data.get("catalogs", []):
                text = f"{catalog['name']} -price : {catalog['price']}- ({catalog['type']})\n{catalog['content']}"
                embedding = await self.get_embedding(text)
                embeddings_data["catalogs"].append({
                    "id": catalog["id"],
                    "embedding": embedding,
                    "metadata": {"type": catalog["type"], "name": catalog["name"]}
                })

            for qa in data.get("qa_pairs", []):
                text = f"Q: {qa['question']}\nA: {qa['answer']}"
                embedding = await self.get_embedding(text)
                embeddings_data["qa_pairs"].append({
                    "id": str(qa["id"]) if isinstance(qa["id"], uuid.UUID) else qa["id"],
                    "embedding": embedding,
                    "metadata": {"type": "qa", "question": qa["question"]}
                })

            blob_name = f"{client_id}/embeddings.json"
            blob_client = self.container.get_blob_client(blob_name)
            
            # Use custom encoder for JSON serialization
            json_data = json.dumps(embeddings_data, cls=self.json_encoder)
            blob_client.upload_blob(json_data, overwrite=True)

            return embeddings_data

        except Exception as e:
            logging.error(f"Error processing embeddings: {str(e)}")
            raise

@dataclass
class QuestionRequest:
    id: str
    question: str
    format: str
    clientId: str
    userId: str
    context: str
    max: int

@dataclass
class AnswerResponse:
    answer: str
    noAnswer: bool
    source: Optional[str]
    clientId: str

from bs4 import BeautifulSoup
import re

def clean_html_content(html_content: str) -> str:

    """
    Clean HTML content by removing tags and preserving meaningful text.
    
    Args:
        html_content (str): Raw HTML content
        
    Returns:
        str: Cleaned text content with preserved spacing and structure
    """

    # Create BeautifulSoup object
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Remove script and style elements
    for script in soup(["script", "style"]):
        script.decompose()
    
    # Get text content
    text = soup.get_text()
    
    # Clean up whitespace
    lines = (line.strip() for line in text.splitlines())
    chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
    text = ' '.join(chunk for chunk in chunks if chunk)
    
    # Remove multiple spaces
    text = re.sub(r'\s+', ' ', text)
    
    # Remove any remaining HTML entities
    text = re.sub(r'&[a-zA-Z]+;', '', text)
    
    return text.strip()




class DataProcessor:


    def __init__(self):
        self.blob_service = BlobServiceClient.from_connection_string(Config.CONNECTION_STRING)
        self.container = self.blob_service.get_container_client(Config.BLOB_CONTAINER)
        self._executor = ThreadPoolExecutor(max_workers=3)
        self.openai_client = AsyncOpenAI(api_key=Config.OPENAI_API_KEY)
        self.embeddings_processor = EmbeddingsProcessor(self.openai_client, self.blob_service)
        # Create SQLAlchemy engine
        connection_url = URL.create(
            "mssql+pyodbc",
            username="fouzir",
            password="Changeme78",
            host="3geniidev.database.windows.net",
            database="QA_Slicky_Admin",
            query={
                "driver": "ODBC Driver 17 for SQL Server",
                "TrustServerCertificate": "no",
                "Encrypt": "yes",
            },
        )
        self.engine = create_engine(connection_url)
        self.Session = sessionmaker(bind=self.engine)        
        self.qa_generator = QAGenerator(self.openai_client, self.Session)




    async def fetch_database_data(self, client_id: str, full_data: bool = False) -> Dict:
        """
        Fetch data from database using SQLAlchemy
        
        Args:
            client_id: The client identifier
            full_data: If True, fetch all data; if False, only fetch unprocessed data
        """
        session = self.Session()
        try:
            # Base queries
            pages_query = session.query(QnaPage).filter(QnaPage.clientId == client_id)
            catalogs_query = session.query(QnaCatalog).filter(QnaCatalog.clientId == client_id)
            qa_pairs_query = session.query(QuestionAnswer).filter(QuestionAnswer.clientId == client_id)
            
            # Add processed filter if not fetching full data
            if not full_data:
                pages_query = pages_query.filter(QnaPage.processed == False)
                catalogs_query = catalogs_query.filter(QnaCatalog.processed == False)
                qa_pairs_query = qa_pairs_query.filter(QuestionAnswer.processed == False)
            
            # Execute queries
            pages = pages_query.all()
            catalogs = catalogs_query.all()
            qa_pairs = qa_pairs_query.all()
            
            # Convert to dictionaries
            pages_dict = []
            for row in pages:
                page_data = {k: v for k, v in row.__dict__.items() if not k.startswith('_')}
                if 'content' in page_data:
                    page_data['content'] = clean_html_content(page_data['content'])
                pages_dict.append(page_data)




            catalogs_dict = [{k: v for k, v in row.__dict__.items() if not k.startswith('_')} 
                           for row in catalogs]
            qa_pairs_dict = [{k: v for k, v in row.__dict__.items() if not k.startswith('_')} 
                           for row in qa_pairs]
            
            return {
                "pages": pages_dict,
                "catalogs": catalogs_dict,
                "qa_pairs": qa_pairs_dict
            }
            
        finally:
            session.close()

    async def load_processed_data(self, client_id: str) -> Dict:
        """Load processed data from blob storage"""
        try:
            blob_name = f"{client_id}/processed_data.json"
            blob_client = self.container.get_blob_client(blob_name)
            
            # Download and decode the blob content
            downloaded_blob = blob_client.download_blob()
            content = downloaded_blob.readall()
            
            # If content is bytes, decode to string
            if isinstance(content, bytes):
                content = content.decode('utf-8')
            
            # Parse JSON with custom decoder for any special types
            return json.loads(content)
            
        except Exception as e:
            logging.error(f"Error loading processed data: {str(e)}")
            # If file doesn't exist or other error, fetch from database
            return await self.fetch_database_data(client_id, full_data=True)

    async def store_processed_data(self, client_id: str, data: Dict):
        """Store processed data in blob storage"""
        try:
            blob_name = f"{client_id}/processed_data.json"
            blob_client = self.container.get_blob_client(blob_name)
            
            # Use CustomJSONEncoder for proper serialization
            blob_client.upload_blob(json.dumps(data, cls=CustomJSONEncoder), overwrite=True)
            
        except Exception as e:
            logging.error(f"Error storing data: {str(e)}")
            raise

    async def update_processed_status(self, client_id: str):
        """Update processed status for all records"""
        session = self.Session()
        try:
            # Update Pages
            session.query(QnaPage).filter(
                QnaPage.clientId == client_id,
                QnaPage.processed == False
            ).update({QnaPage.processed: True})
            
            # Update Catalogs
            session.query(QnaCatalog).filter(
                QnaCatalog.clientId == client_id,
                QnaCatalog.processed == False
            ).update({QnaCatalog.processed: True})
            
            # Update QA pairs
            session.query(QuestionAnswer).filter(
                QuestionAnswer.clientId == client_id,
                QuestionAnswer.processed == False
            ).update({QuestionAnswer.processed: True})
            
            # Commit all changes
            session.commit()
            
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()

    async def process_client(
        self, 
        client_id: str, 
        options: Optional[ProcessingOptions] = None,
        num_qa_pairs: int = Config.NUMBER_OF_QA_PAIRS
    ) -> Dict:
        if options is None:
            # Default to process only unprocessed data
            options = ProcessingOptions(
                handle_unanswered=True,
                generate_new=True,
                process_embeddings=True,
                full_data=False
            )

        result = {
            "auto_answered_count": 0,
            "need_manual_answer_count": 0,
            "new_qa_pairs_count": 0
        }

        try:
            # Fetch data with full_data option
            data = await self.fetch_database_data(client_id, options.full_data)

            if options.process_embeddings:
                await self.store_processed_data(client_id, data)
                await self.embeddings_processor.process_and_store_embeddings(client_id, data)
            
            if (options.handle_unanswered or options.generate_new):
                data = await self.fetch_database_data(client_id, full_data=True)

            if options.handle_unanswered:
                auto_answered, need_manual = await self.qa_generator.handle_unanswered_questions(client_id)
                result["auto_answered_count"] = len(auto_answered)
                result["need_manual_answer_count"] = len(need_manual)
            
            if options.generate_new:
                new_qa_pairs = await self.qa_generator.generate_qa_pairs(data, client_id, num_qa_pairs)
                
                session = self.Session()
                try:
                    for qa_pair in new_qa_pairs:
                        new_qa = QuestionAnswer(**qa_pair)
                        session.add(new_qa)
                    session.commit()
                    result["new_qa_pairs_count"] = len(new_qa_pairs)
                except Exception as e:
                    session.rollback()
                    raise e
                finally:
                    session.close()

            if options.process_embeddings:
                await self.update_processed_status(client_id)
            
            result["success"] = True
            result["data_counts"] = {
                "pages": len(data["pages"]),
                "catalogs": len(data["catalogs"]),
                "qa_pairs": len(data["qa_pairs"])
            }
            return result
            
        except Exception as e:
            logging.error(f"Error in process_client: {str(e)}")
            result["success"] = False
            result["error"] = str(e)
            raise
