from sqlalchemy import create_engine, Column, Integer, String, Text, Float, DateTime, Boolean, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.engine.url import URL
from sqlalchemy.dialects.mssql import UNIQUEIDENTIFIER
import uuid

Base = declarative_base()

# SQLAlchemy Models
class QnaPage(Base):
    __tablename__ = 'QnaPages'
    
    id = Column(Integer, primary_key=True)
    name = Column(String)
    content = Column(Text)
    pageDate = Column(DateTime)
    clientId = Column(String)
    processed = Column(Boolean, default=False)

class QnaCatalog(Base):
    __tablename__ = 'QnaCatalogs'
    
    id = Column(Integer, primary_key=True)
    name = Column(String)
    type = Column(String)
    content = Column(Text)
    price = Column(Float)
    datestamp = Column(DateTime)
    clientId = Column(String)
    processed = Column(Boolean, default=False)
    
def generate_uuid():
    """Generate a new UUID that's compatible with SQL Server's UNIQUEIDENTIFIER"""
    return str(uuid.uuid4()).upper()

class QuestionAnswer(Base):
    __tablename__ = 'QuestionAnswers'
    id = Column(UNIQUEIDENTIFIER, primary_key=True, default=generate_uuid)
    question = Column(Text)
    answer = Column(Text)
    questionSource = Column(String)
    answerSource = Column(String)
    processed = Column(Boolean, default=False)
    clientId = Column(String)
    requestManualAnswer = Column(Boolean, default=False)
