import logging
import json
import aiohttp
import asyncio
import azure.functions as func

def chunk_sections(total_sections, num_chunks=3):
    # Calculate the approximate size of each chunk
    chunk_size = (total_sections + num_chunks - 1) // num_chunks
    return [list(range(i, min(i + chunk_size, total_sections))) for i in range(0, total_sections, chunk_size)]

async def fetch(session, url, payload, headers):
    async with session.post(url, data=json.dumps(payload), headers=headers) as response:
        return await response.text()

async def main_logic(url, company_info, sections, headers):
    section_indices_list = chunk_sections(len(sections))

    async with aiohttp.ClientSession() as session:
        tasks = []
        for section_indices in section_indices_list:
            payload = {
                "companyName": company_info['companyName'],
                "companyEmail": company_info['companyEmail'],
                "companyPhone": company_info['companyPhone'],
                "companyAddress": company_info['companyAddress'],
                "businessType": company_info['businessType'],
                "description": company_info['description'],
                "pageDescription": company_info['pageDescription'],
                "sectionsToUpdate": section_indices,
                "sections": sections
            }
            print(f"Payload: {payload}")
            tasks.append(fetch(session, url, payload, headers))
        results = await asyncio.gather(*tasks)
        return results

def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Python HTTP trigger function processed a request.')

    try:
        req_body = req.get_json()
    except ValueError:
        return func.HttpResponse("Invalid JSON input.", status_code=400)

    company_info = {
        'companyName': req_body.get('companyName', ''),
        'companyEmail': req_body.get('companyEmail', ''),
        'companyPhone': req_body.get('companyPhone', ''),
        'companyAddress': req_body.get('companyAddress', ''),
        'businessType': req_body.get('businessType', ''),
        'description': req_body.get('description', ''),
        'pageDescription': req_body.get('pageDescription', '')
    }
    sections = req_body.get('sections', [])

    # url = "https://ai-slicky-onboarding.azurewebsites.net/api/updateSectionContent?code=C488jhHefppwiwoeFtNnTL_WqN6aTAA2EX1bjNkFC_70AzFuE2d3hA%3D%3D"
    url = "http://localhost:7071/api/updateSectionContent?code=C488jhHefppwiwoeFtNnTL_WqN6aTAA2EX1bjNkFC_70AzFuE2d3hA%3D%3D"
    # url = "https://ai-slicky-onboarding-flex.azurewebsites.net/api/updateSectionContent?code=mZStyEivuCTPJnWIdmWGkQukHaGDYOPhlhD5bpDeV6GXAzFu_-eSxA%3D%3D"
    headers = {'Content-Type': 'application/json'}

    # Create and use a new event loop
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    results = loop.run_until_complete(main_logic(url, company_info, sections, headers))
    loop.close()
    finalsections = []
    finalsections.extend([json.loads(result) for result in results])
    finalsections_flat = []
    finalsections_flat = [item for sublist in finalsections for item in sublist]
    return func.HttpResponse(
        body=json.dumps(finalsections_flat),
        status_code=200,
        headers={"Content-Type": "application/json"}
    )
