#!/usr/bin/env python3
"""
Test script to demonstrate the realistic keyword clustering functionality
"""

import json

def test_different_keyword_types():
    """Test different types of keywords to show intelligent clustering"""
    
    test_cases = [
        {
            "name": "Local Services",
            "payload": {
                "keywords": ["plumbing services", "digital marketing agency", "house cleaning"],
                "maxCluster": 4,
                "target": "Local",
                "locations": ["New York", "Brooklyn", "Queens"]
            },
            "expected_patterns": [
                "near me variations",
                "in {CITY} patterns", 
                "best [service] {CITY}",
                "{CITY} [service]"
            ]
        },
        {
            "name": "National Specialties",
            "payload": {
                "keywords": ["responsive web design", "machine learning algorithms", "organic skincare"],
                "maxCluster": 4,
                "target": "National",
                "locations": ["California", "Texas", "New York"]
            },
            "expected_patterns": [
                "professional [specialty]",
                "custom [specialty] solutions",
                "best [specialty] services",
                "[specialty] experts"
            ]
        },
        {
            "name": "Global Products",
            "payload": {
                "keywords": ["cloud computing solutions", "sustainable packaging", "ai chatbots"],
                "maxCluster": 4,
                "target": "Global",
                "locations": ["United States", "Canada", "United Kingdom"]
            },
            "expected_patterns": [
                "international [product]",
                "[product] worldwide",
                "global [product] solutions",
                "enterprise [product]"
            ]
        },
        {
            "name": "Mixed Services and Specialties",
            "payload": {
                "keywords": ["seo agency", "responsive design", "content marketing", "wordpress development"],
                "maxCluster": 3,
                "target": "Local",
                "locations": ["Miami", "Orlando", "Tampa"]
            },
            "expected_patterns": [
                "Services get location-based variations",
                "Specialties get descriptive variations"
            ]
        }
    ]
    
    print("=== REALISTIC KEYWORD CLUSTERING TEST CASES ===\n")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"{i}. {test_case['name']}")
        print(f"   Payload: {json.dumps(test_case['payload'], indent=6)}")
        print(f"   Expected Patterns: {test_case['expected_patterns']}")
        print()

def show_expected_outputs():
    """Show examples of expected realistic outputs"""
    
    print("=== EXPECTED REALISTIC OUTPUTS ===\n")
    
    examples = [
        {
            "input": "plumbing services (SERVICE)",
            "target": "Local",
            "expected": [
                "plumbing services near me",
                "best plumbing services in {CITY}",
                "{CITY} plumbing services", 
                "emergency plumbing services {CITY}"
            ]
        },
        {
            "input": "responsive web design (SPECIALTY)",
            "target": "Local", 
            "expected": [
                "professional responsive web design",
                "custom responsive web design solutions",
                "best responsive web design services",
                "responsive web design experts"
            ]
        },
        {
            "input": "digital marketing agency (SERVICE)",
            "target": "National",
            "expected": [
                "top digital marketing agency in {STATE}",
                "best digital marketing agency {STATE}",
                "{STATE} digital marketing agency",
                "digital marketing agency services {STATE}"
            ]
        },
        {
            "input": "machine learning algorithms (SPECIALTY)",
            "target": "Global",
            "expected": [
                "advanced machine learning algorithms",
                "custom machine learning algorithms",
                "enterprise machine learning algorithms",
                "machine learning algorithms solutions"
            ]
        }
    ]
    
    for example in examples:
        print(f"Input: {example['input']}")
        print(f"Target: {example['target']}")
        print("Expected Output:")
        for variation in example['expected']:
            print(f"  - {variation}")
        print()

def show_location_variable_system():
    """Explain the location variable system"""
    
    print("=== LOCATION VARIABLE SYSTEM ===\n")
    
    print("The system uses intelligent location variables:")
    print("• Local Target: {CITY} - replaced with specific cities")
    print("• National Target: {STATE} - replaced with states/regions") 
    print("• Global Target: {COUNTRY} - replaced with countries")
    print()
    
    print("Benefits:")
    print("• More efficient than repeating location names")
    print("• Cleaner, more professional output")
    print("• Easier to process and replace in post-processing")
    print("• Consistent formatting across all variations")
    print()

if __name__ == "__main__":
    test_different_keyword_types()
    show_expected_outputs()
    show_location_variable_system()
