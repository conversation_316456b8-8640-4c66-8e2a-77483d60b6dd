import openai
import json
import random

import azure.functions as func

import openai
from openai import OpenAI
from scipy import spatial

client = OpenAI(
  
  api_key ='********************************************************',
)

def get_embedding(text, model="text-embedding-ada-002"):
    global client
    return client.embeddings.create(input=[text], model=model).data[0].embedding
def cosine_similarity(embedding1,embedding2):
    cos_sim = 1 - spatial.distance.cosine(embedding1, embedding2)
    return cos_sim


categories = ['News and media', 'Advertising-Public Relations', 'Food & Drink & Hospitality', 'Sports & Fitness', 'Animal Related', 'Arts & Entertainment', 'Retail', 'Conferences & Events', 'Education', 'Information Technology', 'Non-Profit & Government', 'Finance & Insurance & Legal', 'Automotive & Planes', 'Real Estate', 'Health & Wellness & Beauty', 'Energy, Utilities & Resources', 'Travel & Tourism', 'Engineering & Construction', 'Religion']


category_plugins_mapping = {"News and media": [  "Unlimited Slicky Translate",  "Free SEO",  "Unlimited Online Store"],"Advertising-Public Relations": [  "Limited Slicky Translate",  "Limited SEO",  "Limited Online Store"],"Food & Drink & Hospitality": [  "Limited Slicky Translate",  "Limited Online Store"],"Sports & Fitness": [  "Limited Slicky Translate",  "Limited Online Store"],"Animal Related": [  "Limited Slicky Translate",  "Limited Online Store"],"Arts & Entertainment": [  "Limited Slicky Translate",  "Limited Online Store"],"Retail": [  "Unlimited Slicky Translate",  "Unlimited Online Store"],"Conferences & Events": [  "Unlimited Slicky Translate",  "Unlimited Online Store"],"Education": [  "Limited Slicky Translate",  "Limited SEO",  "Limited Online Store"],"Information Technology": [  "Unlimited Slicky Translate",  "Free SEO",  "Unlimited Online Store"],"Non-Profit & Government": [  "Limited Slicky Translate",  "Limited SEO",  "Limited Online Store"],"Finance & Insurance & Legal": [  "Unlimited Slicky Translate",  "Unlimited Online Store"],"Automotive & Planes": [  "Limited Slicky Translate",  "Limited Online Store"],"Real Estate": [  "Unlimited Slicky Translate",  "Unlimited Online Store"],"Health & Wellness & Beauty": [  "Limited Slicky Translate",  "Limited Online Store"],"Energy, Utilities & Resources": [  "Unlimited Slicky Translate",  "Unlimited Online Store"],"Travel & Tourism": [  "Limited Slicky Translate",  "Limited Online Store"],"Engineering & Construction": [  "Limited Slicky Translate",  "Limited Online Store"],"Religion": [  "Limited Slicky Translate",  "Limited Online Store"]}

plugins_responses = [
    "Consider the following plugins to enhance your website's functionality\nFeel free to select the ones that align with your website's requirements.",
    "To improve your website's performance, take a look at these plugins\nTick the checkboxes next to the plugins you want to include.",
    "Here's a selection of plugins to elevate your website's capabilities\nSimply choose the plugins you'd like to integrate.",
    "We've curated a list of powerful plugins for your website\nSelect the ones you want to incorporate into your site."
]
selected_plugins_responses = ["Great choice! You have selected {selected_plugins} for your website. We'll make sure they are seamlessly integrated for you.",
"Perfect! You've chosen {selected_plugins} to enhance your website's functionality. We'll get to work on integrating them as per your preferences.",
"Thank you for selecting {selected_plugins}. We'll ensure these plugins are set up on your website to boost its performance.",
"Confirmed! {selected_plugins} have been added to your website. If you have any further preferences, just let us know!",
"Excellent! You've opted for {selected_plugins} to power up your website. We'll take care of the rest!",
"Thanks for your choices! {selected_plugins} will be implemented on your website to improve its capabilities.",
"Your selections have been recorded! You'll soon enjoy the benefits of {selected_plugins} on your website.",
"Noted! {selected_plugins} are now part of your website's feature set. If you need any adjustments, feel free to reach out.",
"Confirmed preferences: {selected_plugins}. Our team will work on integrating them to optimize your website.",
"Excellent picks! {selected_plugins} will be incorporated into your website to make it even more powerful. Let us know if there's anything else we can assist you with!"]


class DomainSuggestionObject:
    def __init__(self, firstname, lastname, business_name, description):
        self.firstname = firstname
        self.lastname = lastname
        self.business_name = business_name
        self.description = description


def validate_required_fields(data):
    required_fields = ['question', 'context', 'iteration']
    missing_fields = [field for field in required_fields if field not in data]

    if missing_fields:
        return func.HttpResponse(json.dumps({'error': f"Missing required field(s): {', '.join(missing_fields)}"}), status_code = 400)

    if int(data["iteration"])>6:
        return func.HttpResponse(json.dumps({'error': f"Max iterations exceeded"}), status_code = 400)

    return None


def validate_optional_fields(data):
    # optional_fields = ['personName', 'businessName', 'businessDescription', 'wisheddomain', 'olddomain']
    # invalid_fields = [field for field in optional_fields if field in data and not isinstance(data[field], str)]

    # if 'variables' in data and not isinstance(data['variables'], list):
    #     invalid_fields.append('variables')

    # if invalid_fields:
    #     return func.HttpResponse(json.dumps({'error': f"Invalid data for optional field(s): {', '.join(invalid_fields)}"}), status_code = 400)

    return None


def is_question_valid(question, context):
    # Check if the question is valid and in sync with the context
    # Replace with your validation logic
    return True

def ask_user_more_infos (question,additional_data):
    # Load additional information about user/company and business description
    # and proposed domain names already
    # Replace with your logic to fetch additional data

    conversation = []
    possible_informations = []
    for key, value in additional_data.items():
        if value and value != 'NULL':
            conversation.append((key, value))
        else:
            if key in ['businessName', 'businessDescription'] :
                possible_informations.append(key)


    non_null_keys = [key for key, value in additional_data.items() if  key in ['businessName', 'businessDescription'] and value is None or value == "NULL"]


    possible_informations = ""
    if non_null_keys != []:
        possible_informations = ",".join(non_null_keys)
    if(len(conversation) > 0 ) :
        log = "USER: i want you to choose the good category for my business\nBOT: What is your {}?\n".format(conversation[0][0])
        for i in range(len(conversation)):
            if i == 0:
                log += "USER: My {} is {}\n".format(conversation[i][0], conversation[i][1])
            else:
                log += "BOT: What is your {}?\nUSER: My {} is :{}\n".format(conversation[i][0], conversation[i][0], conversation[i][1])
    else:
        log = f"""USER : {question}"""

    # missingfields_with_null_values = {key: value for key, value in missingfields.items() if value == "NULL" or value == "null" or value is None }
    # missingfields_with_null_values["userIntent"] = "based on only user_input choose one of the intents : user_have_different_intent_and_dont_want_tohave_domain|user_not_satisfied_with_suggestion|user_explains_his_business_to_have_accurate_suggestion|user_have_specific_domaine_that_he_wants|user_want_to_stop|user_want_to_contact_admin"
    gpt_prompt = f"""
    for the giving conversation suggest a bot_response (as a json with one key bot_response) to ask user more informations to give him accurate business category
    ------------
    CONVERSATION:
    BOT : Hi ,i'm Sara ,now i will help you choose an accurate category for your business to create your website
    {log}
    BOT : ( bot_response will goes here)
    ------------
    possible informations : {possible_informations}
    ------------
    NB1 :  if you already have the person name you can use it in your bot_response if not ask user for its name first
    NB2 : return a valid json file that can be loaded
    Output json:
    """


    iter = 0
    while (iter < 3):
        try:

            messages = [{"role":"user","content":gpt_prompt}]
            result = client.chat.completions.create(
                model="gpt-4.1-mini",
                messages=messages
            )
            response_additional_data = json.loads(result.choices[0].message.content)
            # Update the missing_fields dictionary with the filled values
            additional_data["response"] = response_additional_data["bot_response"]
            break
        except:
            iter +=1
            continue


    return additional_data
def generateModules(additional_data,business_categroy):

    plugins =  category_plugins_mapping.get(business_categroy,None)
    additional_data["variables"] = plugins
    additional_data["businessCategory"] = business_categroy

    additional_data['response'] = random.choice(plugins_responses)
    return additional_data

def get_additional_data(question, additional_data):
    # Load additional information about user/company and business description
    # and proposed domain names already
    # Replace with your logic to fetch additional data
    global categories




    conversation = []
    for key, value in additional_data.items():
        if value and value != 'NULL':
            conversation.append((key, value))
    if(len(conversation) > 0 ) :
        log = "USER:i want you to choose the good category for my business\nBOT: What is your {}?\n".format(conversation[0][0])
        for i in range(len(conversation)):
            if i == 0:
                log += "USER: My {} is {}\n".format(conversation[i][0], conversation[i][1])
            else:
                if(conversation[i][0] == "variables"):
                    for ii,variable in enumerate(conversation[i][1]):
                        if ii == len(conversation[i][1])-1:
                            log += "BOT: What do you thing about Category : {}?\nUSER: {}\n".format(variable,question)
                        else:
                            log += "BOT: What do you thing about Category : {}?\nUSER: Nah it doesn't fit my business activity \n".format(variable)

                else:
                    log += "BOT: What is your {}?\nUSER: My {} is :{}\n".format(conversation[i][0], conversation[i][0], conversation[i][1])

        if(additional_data["variables"] == []):
            log += "BOT: Tell me more about your business to give you acurrate  ?\nUSER: {}\n".format(question)

    else:
        log = f"""USER : {question}"""


    if( additional_data["variables"] != None  and len(additional_data["variables"])>0):
        # categories = [x for x in categories if x not in additional_data["variables"]]
        additional_data["businessDescription"] = 'NULL'
        additional_data =  ask_user_more_infos(question,additional_data)

    str_categories = str(categories)

    # missingfields_with_null_values = {key: value for key, value in missingfields.items() if value == "NULL" or value == "null" or value is None }
    # missingfields_with_null_values["userIntent"] = "based on only user_input choose one of the intents : user_have_different_intent_and_dont_want_tohave_domain|user_not_satisfied_with_suggestion|user_explains_his_business_to_have_accurate_suggestion|user_have_specific_domaine_that_he_wants|user_want_to_stop|user_want_to_contact_admin"
    gpt_prompt = f"""
    from conversation extract informations from user input about the userand his business that match giving json file and replace their values in the correspandent fields, return back filled json object
    leave "NULL" for empty elements
    ------------
    CONVERSATION:
    BOT : Hi ,i'm Sara , your assistant to create your website is there anything i can help you with
    {log}
    BOT : ( bot_response will goes here)
    ------------
    list of categories :
    {str_categories}
    ------------
    data json : {additional_data}
    NB : try to fill businessName,businessDescription,businessCategory with any releavant information from the conversation i fthere exists else 'NULL'
    NB2 : for businessCategory give an array of  multiple categories that may be interresting for business website if they match giving business informations , if nothing return 'NULL'
    NB4 : return a valid json file that can be loaded

    Output json:
    """

    print("2 . additional_data prompt",gpt_prompt,flush=True)
    iter = 0
    while (iter < 3):
        try:

            messages = [{"role":"user","content":gpt_prompt}]
            result = client.chat.completions.create(
                model="gpt-4.1-mini",
                messages=messages
            )
            response_additional_data = json.loads(result.choices[0].message.content)
            break
        except:
            iter +=1
            continue

    print("3 . additional_data response_additional_data",response_additional_data,flush=True)

    # Update the missing_fields dictionary with the filled values
    for key, value in response_additional_data.items():
        if value and value != 'NULL':
            additional_data[key] = value
        else:
            if( not additional_data[key] or additional_data[key] == 'NULL'):
                additional_data[key] = None

    if (additional_data["businessDescription"]!= None and additional_data["businessDescription"]!= 'NULL') :
        # similarities = get_categories(question,additional_data)
        gpt_categories = additional_data["businessCategory"]
        # threshold = 0
        # additional_data["variables"] = None
        # if(isinstance(gpt_categories, list)):
        #     threshold = len(gpt_categories)
        # matched_category = sorted(similarities, key=lambda x: x[1], reverse=True)[0]
        matched_category = gpt_categories[0]
        if(matched_category != "NULL"):
            additional_data = generateModules(additional_data,matched_category)

    print("3 . additional_data resullt",additional_data,flush=True)

    return additional_data

def has_enough_information(additional_data):

    person_name = additional_data['personName']
    business_name = additional_data['businessName']
    business_description = additional_data['businessDescription']
    business_category = additional_data['businessCategory']
    # Check if either wished_domain or old_domain is provided


    if business_description != None and business_description!= "NULL" and business_category != None and business_category!= "NULL":
        return True

    return False

def format_domain(domain):
    if not domain :
        return None
    # Remove spaces
    domain = domain.replace(" ", "")

    # Remove "www" if present
    domain = domain.replace("www.", "")

    # Remove "http://" or "https://" if present
    if domain.startswith("http://"):
        domain = domain[7:]
    elif domain.startswith("https://"):
        domain = domain[8:]

    # Remove the extension
    parts = domain.split(".")
    main_part = ".".join(parts[:-1])



    if (main_part == "" or main_part == None or main_part == "NULL" or main_part == "null" or main_part == "null"):
        return None
    return main_part.lower()

# def get_categories (user_input,additional_data):
#     # global categories_embeddings

#     query_embedding_response = openai.Embedding.create(
#         model="text-embedding-ada-002",
#         input=user_input,
#     )
#     query_embedding = query_embedding_response["data"][0]["embedding"]
#     result = []

#     if( additional_data["variables"] != None  and len(additional_data["variables"])>0):
#         categories_embeddings = [x for x in categories_embeddings if x not in additional_data["variables"] ]

#     for emb in categories_embeddings:
#         similarity = cosine_similarity(query_embedding, emb["category_embedding"])
#         result.append((emb["category"],similarity))
#     return result


def conformity_check2(old_conv, response_additional_data):
    gpt_prompt = f"""
    1 . check json_data json values (not null ones) , if are conform and seems to be extracted from conversation not some random generated data
    2 . extract a suggested domain if exists in the finale bot_response message else put 'NULL'. if there is multiple suggestions choose the last one only
    return json object with one tow keys is_conform, suggested_domain based on  that tow criterias
    ------------
    CONVERSATION:
    {old_conv}
    ------------
    json_data {response_additional_data}
    ------------
    NB2 : return a valid json file that can be loaded
    Output json:
    """


    iter = 0

    while (iter < 3):
        try:

            messages = [{"role":"user","content":gpt_prompt}]
            result = client.chat.completions.create(
                model="gpt-4.1-mini",
                messages=messages
            )
            response_additional_data = json.loads(result.choices[0].message.content)
            # Update the missing_fields dictionary with the filled values



            if( response_additional_data["suggested_domain"] and (response_additional_data["is_conform"] == True or response_additional_data["is_conform"] == False) ) :
                return response_additional_data
                break
            else:
                iter +=1
                continue
        except:
            iter +=1
            continue
    return False




# def make_openai_call(gpt_prompt, num_retries=3):
#     try:
#         messages = [{"role": "user", "content": gpt_prompt}]
#         response = requests.get(
#             "https://api.openai.com/v1/chat/completions",
#             headers={
#                 "Authorization": f"Bearer {openai.api_key}",
#                 "Content-Type": "application/json"
#             },
#             json={
#                 "model": "gpt-4.1-mini	",
#                 "messages": messages
#             },
#             timeout=10  # Timeout value in seconds
#         )

#         response.raise_for_status()
#         result = response.json()
#         response_additional_data = json.loads(result["choices"][0]["message"]["content"])
#         return response_additional_data

#     except Timeout:
#         # Timeout error
#         if num_retries > 0:
#             print("Request timed out. Retrying in 10 seconds...")
#             time.sleep(0.5)
#             return make_openai_call(gpt_prompt, num_retries - 1)
#         else:
#             print("Max retries exceeded.")
#             return None

#     except requests.exceptions.RequestException as e:
#         # Other request errors
#         if num_retries > 0:
#             print("An error occurred:", e)
#             print("Retrying in 10 seconds...")
#             time.sleep(0.5)
#             return make_openai_call(gpt_prompt, num_retries - 1)
#         else:
#             print("Max retries exceeded.")
#             return None

def handle_get_modules(data):

    error_response = validate_optional_fields(data)
    if error_response:
        return error_response

    question = data['question']
    context = data['context']
    iteration = data['iteration']
    variables = data.get('variables', [])
    person_name = data.get('personName', 'NULL')
    business_name = data.get('businessName', 'NULL')
    business_description = data.get('businessDescription', 'NULL')
    business_category = data.get('businessCategory', 'NULL')


    if variables == None:
        variables = []
    if not is_question_valid(question, context):
        return func.HttpResponse(json.dumps({'error': 'Invalid question for GetDomainVariables context'}), status_code = 400)


    additional_data = {
        'personName': person_name,
        'businessName': business_name,
        'businessDescription': business_description,
        'variables': variables,
        'response': 'NULL',
        'businessCategory': business_category,
    }


    print("wiiiwiwiwiw2 "+str(business_category),flush=True)


    if(additional_data["businessCategory"] != None and additional_data["businessCategory"] != "NULL"):
        return generateModules(additional_data,additional_data["businessCategory"])
    else:

        additional_data = get_additional_data(question, additional_data)

        if not has_enough_information(additional_data):
            additional_data =  ask_user_more_infos(question,additional_data)
        # return func.HttpResponse(json.dumps({'error': 'Not enough information for domain suggestion'}), status_code = 400)

    # domain_suggestion = DomainSuggestionObject(
    #     firstname=additional_data['person_name'],
    #     lastname='',
    #     business_name=additional_data['business_name'],
    #     description=additional_data['business_description'],
    #     wisheddomain=additional_data['wished_domain'],
    #     olddomain=additional_data['old_domain']
    # )

    # if generate_domain_suggestion(additional_data):
    #     variables += generate_domain_suggestion(additional_data)
    # response_variables = generate_domain_suggestion(additional_data)
    # Continue with further processing using the domain_suggestion object and additional_data
    # ...

    response = {
        'variables': additional_data['variables'],
        'personName': additional_data['personName'],
        'businessName': additional_data['businessName'],
        'businessDescription': additional_data['businessDescription'],
        'businessCategory': additional_data["businessCategory"],
        'question': question,
        'response': additional_data['response'],
        'context': context,
        'iteration': iteration+1
    }
    # response = {
    #     'variables': variables,
    #     'personName': person_name,
    #     'businessName': business_name,
    #     'businessDescription': business_description,
    #     'wisheddomain': wished_domain,
    #     'olddomain': old_domain,
    #     'question': question,
    #     'context': context,
    #     'iteration': iteration
    # }

    return func.HttpResponse(json.dumps(response), mimetype="application/json", status_code=200 )

def list_to_str(lst):
    if not lst:
        return ""
    elif len(lst) == 1:
        return lst[0]
    elif len(lst) == 2:
        return f"{lst[0]} and {lst[1]}"
    else:
        middle_elements = ", ".join(lst[:-1])
        return f"{middle_elements}, and {lst[-1]}"

def handle_save_modules(data):

    error_response = validate_optional_fields(data)
    if error_response:
        return error_response

    question = data['question']
    context = data['context']
    iteration = data['iteration']
    variables = data.get('variables', [])
    person_name = data.get('personName', 'NULL')
    business_name = data.get('businessName', 'NULL')
    business_description = data.get('businessDescription', 'NULL')
    business_category = data.get('businessCategory', 'NULL')
    wished_domain = data.get('wisheddomain', 'NULL')
    old_domain = data.get('olddomain', 'NULL')

    if variables == None:
        variables = []

    if len(variables) == 0:
        func.HttpResponse(json.dumps({'error': f"No selected Plugins found"}), status_code = 400)


    selected_plugins = variables

    response = random.choice(selected_plugins_responses).replace("{selected_plugins}",list_to_str(selected_plugins))



    additional_data = {
        'personName': person_name,
        'businessName': business_name,
        'businessDescription': business_description,
        'businessCategory': business_category,
        'variables': variables,
        'response': response,
        'iteration': iteration+1,
        'context':context,
        'question':question
    }




    return func.HttpResponse(json.dumps(additional_data), mimetype="application/json", status_code=200 )


