#!/usr/bin/env python3
"""
Test script for the GeneratePageDescriptions Azure Function
"""

import json
import sys
import os

# Add the function directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'GeneratePageDescriptions'))

# Mock Azure Functions request for testing
class MockHttpRequest:
    def __init__(self, json_body):
        self._json_body = json_body
    
    def get_json(self):
        return self._json_body

def test_page_descriptions():
    """Test the page descriptions function with sample data"""
    
    # Import the function
    from GeneratePageDescriptions import main
    
    # Test data
    test_payload = {
        "keywords": [
            {
                "keyword": "iPhone repair",
                "cluster": ["iPhone repair near me", "iPhone screen replacement", "iPhone battery replacement", "water damage iPhone repair"]
            },
            {
                "keyword": "web development",
                "cluster": ["professional web development", "custom web applications", "e-commerce development", "web development services"]
            },
            {
                "keyword": "digital marketing agency",
                "cluster": ["digital marketing agency near me", "social media marketing", "PPC advertising", "content marketing services"]
            }
        ]
    }
    
    # Create mock request
    mock_req = MockHttpRequest(test_payload)
    
    # Call the function
    try:
        response = main(mock_req)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.get_body().decode()}")
        
        if response.status_code == 200:
            result = json.loads(response.get_body().decode())
            print("\nParsed Response:")
            print(json.dumps(result, indent=2))
            
            # Show expected page types
            print("\nExpected Page Analysis:")
            for page in result.get('pages', []):
                print(f"- {page['pageTitle']}")
                print(f"  Type: {page['pageType']}")
                print(f"  Description: {page['description']}")
                print()
        
    except Exception as e:
        print(f"Error testing function: {e}")

def test_validation():
    """Test input validation"""
    from GeneratePageDescriptions import main
    
    # Test missing fields
    test_cases = [
        {},  # Empty payload
        {"keywords": []},  # Empty keywords
        {"keywords": [{"keyword": "test"}]},  # Missing cluster field
        {"keywords": [{"cluster": ["test"]}]},  # Missing keyword field
        {"keywords": [{"keyword": "", "cluster": ["test"]}]},  # Empty keyword
    ]
    
    for i, test_case in enumerate(test_cases):
        print(f"\nValidation Test case {i+1}: {test_case}")
        mock_req = MockHttpRequest(test_case)
        response = main(mock_req)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.get_body().decode()}")

def show_expected_page_types():
    """Show examples of expected page types and descriptions"""
    
    print("=== EXPECTED PAGE TYPES AND EXAMPLES ===\n")
    
    examples = [
        {
            "cluster": "iPhone Repair Services",
            "expected_type": "Service",
            "expected_title": "iPhone Repair Services - Professional Mobile Device Repair",
            "expected_description": "Expert iPhone repair services including screen replacement, battery replacement, and water damage repair. Same-day service available with certified technicians."
        },
        {
            "cluster": "Web Development Solutions",
            "expected_type": "Service",
            "expected_title": "Professional Web Development Services - Custom Solutions",
            "expected_description": "Professional web development services creating custom applications and websites. Full-stack development with modern technologies and ongoing support."
        },
        {
            "cluster": "Digital Marketing Strategy",
            "expected_type": "Service",
            "expected_title": "Digital Marketing Services - Comprehensive Online Strategy",
            "expected_description": "Complete digital marketing services including social media, PPC, and content marketing. Data-driven strategies that deliver measurable results."
        }
    ]
    
    for example in examples:
        print(f"Cluster: {example['cluster']}")
        print(f"Expected Type: {example['expected_type']}")
        print(f"Expected Title: {example['expected_title']}")
        print(f"Expected Description: {example['expected_description']}")
        print("-" * 80)

if __name__ == "__main__":
    print("Testing GeneratePageDescriptions function...")
    print("=" * 60)
    
    print("\n1. Testing basic functionality:")
    test_page_descriptions()
    
    print("\n2. Testing validation:")
    test_validation()
    
    print("\n3. Expected page types:")
    show_expected_page_types()
