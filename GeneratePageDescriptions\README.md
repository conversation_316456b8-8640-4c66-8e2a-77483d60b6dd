# GeneratePageDescriptions Azure Function

## Overview
This Azure Function takes keyword clusters as input and generates strategic page descriptions for new pages that should be created on a website. It uses OpenAI GPT-4o-mini to analyze keyword intent and suggest appropriate page types, titles, and descriptions.

## Input Format
```json
{
  "clusters": [
    {
      "name": "Fix iPhone",
      "clusters": ["iPhone repair near me", "iPhone screen replacement", "iPhone battery replacement"]
    },
    {
      "name": "Web Design", 
      "clusters": ["professional web design", "custom web design solutions", "responsive web design services"]
    }
  ]
}
```

## Output Format
```json
{
  "pages": [
    {
      "description": "Expert iPhone repair services including screen replacement, battery replacement, and water damage repair. Same-day service available with certified technicians and warranty coverage.",
      "pageType": "Service",
      "pageTitle": "iPhone Repair Services - Professional Mobile Device Repair"
    },
    {
      "description": "Professional web design services creating responsive, modern websites that drive results. Custom solutions for businesses of all sizes with ongoing support and optimization.",
      "pageType": "Service",
      "pageTitle": "Professional Web Design Services - Custom Website Development"
    }
  ]
}
```

## Page Types
The function can suggest the following page types:
- **Service** - For service-based keywords (repairs, consulting, etc.)
- **Product** - For product-focused keywords
- **Landing** - For targeted marketing campaigns
- **Category** - For broad topic categories
- **Location** - For location-specific content
- **Blog** - For informational/educational content

## Key Features

### 🎯 **Strategic Analysis**
- Analyzes keyword intent to determine optimal page type
- Considers user search intent (informational, commercial, transactional)
- Focuses on conversion opportunities and business goals

### 📝 **Content Generation**
- Creates compelling page descriptions (2-3 sentences)
- Generates SEO-optimized titles (50-60 characters)
- Includes key benefits and value propositions

### 🔍 **SEO Optimization**
- Incorporates primary keywords naturally
- Follows SEO best practices for titles and descriptions
- Ensures content complements existing website pages

### ✅ **Validation**
- Comprehensive input validation
- Error handling for malformed requests
- Structured JSON response format

## Usage Examples

### Service-Based Business
Input clusters for iPhone repair, web design, and SEO services will generate service pages with location-aware content and conversion-focused descriptions.

### E-commerce Business
Input clusters for product categories will generate product and category pages optimized for commercial search intent.

### Content Marketing
Input clusters for educational topics will generate blog and landing pages focused on informational search intent.

## Error Handling
The function includes robust error handling for:
- Invalid JSON payloads
- Missing required fields
- Malformed cluster objects
- OpenAI API failures
- Response validation errors

## Integration
This function is designed to work with the `getKeywordCluster` function output, taking the generated keyword clusters and creating strategic page recommendations based on them.
