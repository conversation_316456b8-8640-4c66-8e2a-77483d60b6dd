import logging
import json
import azure.functions as func
from openai import OpenAI
import json

import json
from bs4 import BeautifulSoup

import json
from bs4 import BeautifulSoup, NavigableString
from bs4 import BeautifulSoup
import json

class HTMLContentParser:
    def __init__(self, sections):
        self.sections = sections
        self.content_list = []
        self.placeholder_count = 0

    def extract_content_with_placeholders(self):
        for section in self.sections:
            modified_soup = BeautifulSoup(str(section["sectionContent"]), 'html.parser')
            for element in modified_soup.find_all(string=True, recursive=True):
                if element.parent.name != 'script':
                    if isinstance(element, NavigableString) and element.strip():
                        self.placeholder_count += 1
                        placeholder_text = f"--placeholder_{self.placeholder_count}--"
                        content = {
                            "type": "text",
                            "identifier": f"placeholder_{self.placeholder_count}",
                            "text": element.strip()
                        }
                        element.replace_with(placeholder_text)
                        self.content_list.append(content)

            for img_tag in modified_soup.find_all('img'):
                if("noreplace" in img_tag.attrs):
                    continue
                if 'alt' in img_tag.attrs or 'keyword' in img_tag.attrs:
                    self.placeholder_count += 1
                    # content = {
                    #     "type": "img",
                    #     "identifier": f"placeholder_{self.placeholder_count}",
                    #     "attributes": {attr: img_tag[attr] for attr in ['alt', 'keywords'] if attr in img_tag.attrs}
                    # }
                    content = {
                        "type": "img",
                        "identifier": f"placeholder_{self.placeholder_count}",
                        "attributes": {attr: "--imagekeyword--" for attr in ['alt', 'keyword']}
                    }
                    for attr in ['alt', 'keyword']:
                        img_tag[attr] = f"--placeholder_{self.placeholder_count}--"
                        if attr in img_tag.attrs:
                            placeholder_text = f"--placeholder_{self.placeholder_count}--"
                            img_tag[attr] = placeholder_text
                    self.content_list.append(content)

            for element in modified_soup.find_all(style=True):
                if("noreplace" in element.attrs):
                    continue
                if 'background' in element['style']:
                    self.placeholder_count += 1
                    placeholder_text = f"--placeholder_{self.placeholder_count}--"
                    content = {
                        "type": "background_image",
                        "identifier": f"placeholder_{self.placeholder_count}",
                        "attributes": {attr: "--imagekeyword--" for attr in ['keyword']}
                    }
                    for attr in ['keyword']:
                        element[attr] = f"--placeholder_{self.placeholder_count}--"
                        if attr in element.attrs:
                            placeholder_text = f"--placeholder_{self.placeholder_count}--"
                            element[attr] = placeholder_text

                    self.content_list.append(content)



            section["sectionContent"] = str(modified_soup)
        return json.dumps(self.content_list, indent=4)

    def generate_html_with_replacements(self, json_content):
        content_replacements = json.loads(json_content)
        updated_sections = []

        for section in self.sections:
            modified_soup = BeautifulSoup(str(section["sectionContent"]), 'html.parser')
            for content in content_replacements:
                placeholder_text = f"--{content['identifier']}--"
                if content['type'] == 'img':
                    for img_tag in modified_soup.find_all("img"):
                        for attr in content['attributes']:
                            if img_tag.get(attr) == placeholder_text:
                                img_tag[attr] = content['attributes'][attr]
                elif content['type'] == 'background_image':
                    for element in modified_soup.find_all(style=True):
                        for attr in content['attributes']:
                            if element.get(attr) == placeholder_text:
                                element[attr] = content['attributes'][attr]
                else:
                    result_content = str(modified_soup).replace(placeholder_text, content['text'])
                    modified_soup = BeautifulSoup(result_content, 'html.parser')

            section["sectionContent"] = str(modified_soup)
            updated_sections.append(section)

        return updated_sections


def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Python HTTP trigger function processed a request.')

    try:
        # Parse the incoming JSON payload
        req_body = req.get_json()

        sections = req_body.get('sections', [])
        company_info = {
            'companyName': req_body.get('companyName', ''),
            'companyEmail': req_body.get('companyEmail', ''),
            'companyPhone': req_body.get('companyPhone', ''),
            'companyAddress': req_body.get('companyAddress', ''),
            'description': req_body.get('description', ''),
            'businessType': req_body.get('businessType', ''),
            'sectionsToUpdate': req_body.get('sectionsToUpdate', []),
            'page_description': req_body.get('pageDescription', '')
        }

        # Process sections using OpenAI
        updated_sections = update_sections_with_openai(sections, company_info)

        # Return the modified sections as JSON
        if(updated_sections == None):
            return func.HttpResponse(
                "An error occurred while processing your request.",
                status_code=500
            )
        return func.HttpResponse(
            body=json.dumps(updated_sections),
            status_code=200,
            mimetype="application/json"
        )
    except ValueError:
        # Handle case where JSON is not parsable
        return func.HttpResponse(
             "Invalid JSON input. Please provide a valid JSON in the request body.",
             status_code=400
        )
    except Exception as e:
        logging.error(f"An error occurred: {str(e)}")
        return func.HttpResponse(
             "An error occurred while processing your request.",
             status_code=500
        )
def get_missing_objects(list1, list2):
    # Create a set of identifiers from list2
    identifiers_set = {item['identifier'] for item in list2}

    # Filter out objects from list1 that are missing in list2 based on identifier
    missing_objects = [item for item in list1 if item['identifier'] not in identifiers_set]

    return missing_objects

def update_sections_with_openai(sections, company_info):
    client = OpenAI(
    api_key ='********************************************************',
    )    
    updated_content = []
    sectionsToUpdate = company_info['sectionsToUpdate']
    if sectionsToUpdate == [] or sectionsToUpdate == None:
        sections = sections
    else:
        sections = [sections[x] for x in sectionsToUpdate]



    parser = HTMLContentParser(sections)
    json_content = parser.extract_content_with_placeholders()

    # Enhanced prompt that includes page_description
    if 'page_description' in company_info:
        prompt = f"""Given the page purpose: {company_info['page_description']}

    Let's approach this step by step:
    1. First, analyze each section's current content and identify its core purpose
    2. Then, determine how to adapt each section to better serve the page's goal
    3. Finally, rewrite the content while preserving the original structure (and same texts lentghs)

    Current sections: {{"sections":{json_content}}}
    Company context: {json.dumps(company_info)}

    Guidelines:
    - For each piece of content, first explain your thought process for changes
    - Then generate the modified content following that reasoning
    - Keep exact JSON structure
    - Modify only text and alt attributes
    - Include relevant image keywords and alt text

    Response format:
    {{"sections": [{{same structure as input with updated content}}]}}"""


    else:
        prompt = f"""Rewrite and generate same json as the giving input for website sections to better reflect the company profile

    Input Sections: {{"sections":{json_content}}}
    Company Info: {json.dumps(company_info)}

    Guidelines:
    - Generate content that reflects company profile
    - Maintain same structure and format as input
    - Modify only text and alt attributes
    - Keep content meaning aligned with input
    - For images: Include keyword for relevant image search and corresponding alt text

    Result with same Input Sections structure:"""

    
    iter = 0
    results = []
    while (iter < 3):
        try:
            messages = [{"role":"user","content":prompt }]
            result = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=messages,
                response_format={"type": "json_object"}

            )
            content = result.choices[0].message.content.strip()
            result = json.loads(content)
            print("json_content:",json_content)
            if "--placeholder" in content:
                iter +=1
                continue                
            if "--imagekeyword--" in result["sections"]:
                iter +=1
                continue  
            missing_objects = get_missing_objects(json.loads(json_content), result["sections"])              
            if missing_objects:
                iter +=1
                page_context = f"Page Context: {company_info.get('page_description', 'No specific page context provided.')}"
                
                prompt = f"""Rewrite and generate same json as the giving input for website sections to better reflect the company profile and specific page purpose
                Using these company info if needed: {json.dumps(company_info)}
                {page_context}
                Input Sections : {{"sections":{json_content}}}
                Note : the idea is to generate content that better reflects the company profile and the specific page purpose
                Note : if page_description is provided, make sure the content aligns with the described page purpose
                Note : you can include informations from the sections in the company info if needed or already used
                Note : be free to generate new content or rewrite the existing one but keep the same size and format
                Note : Change only text and alt attributes of images if needed keep same other fields as the input
                Note : Generated Content meaning should be same as the input content just better reflects the company profile and page purpose
                Note : for keyword put a keyword to search a releavant image that suits the content and a correspendant alt text if needed
                Result with same Input Sections structure:""" 
                   
                messages = [{"role":"user","content":prompt }]
                result2 = client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=messages,
                    response_format={"type": "json_object"}

                )
                content = result2.choices[0].message.content.strip()
                result2 = json.loads(content)
                results.extend(result2["sections"])


            results.extend(result["sections"])



            print("result:",)

            updated_sections = parser.generate_html_with_replacements(json.dumps(results))

            return updated_sections


            # if("sections" in result):
            #     updated_content = {"sections":updated_content}
            #     return updated_content

            # else:
            #     raise Exception("Invalid format")            
        except Exception as e:
            print("error:",e)
            iter +=1
            continue
    return None
    

