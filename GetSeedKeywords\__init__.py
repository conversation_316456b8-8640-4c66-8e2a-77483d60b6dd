# Example payload to call this endpoint:
# {
#   "address": "123 Main St, Springfield, IL",
#   "description": "Local bakery specializing in artisan breads and pastries.",
#   "priority": "I want to drive more traffic",
#   "geography_target": "Springfield, IL, USA",
#   "reach_type": "Quick Wins",
#   "seo_plan": "premium"
# }

import azure.functions as func
import json
import logging
from openai import OpenAI

# Initialize the OpenAI client with your organization and API key as before
client = OpenAI(
  
  api_key ='********************************************************',
)

def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Python HTTP trigger function processed a request to create SEO keywords.')

    try:
        # Parse JSON body
        try:
            req_body = req.get_json()
        except ValueError:
            return func.HttpResponse(json.dumps({'error': 'Invalid JSON body.'}), mimetype="application/json", status_code=400)

        # Extract required fields
        address = req_body.get('address')
        description = req_body.get('description')
        priority = req_body.get('priority')
        geography_target = req_body.get('geography_target')
        reach_type = req_body.get('reach_type')
        seo_plan = req_body.get('seo_plan')

        # Validate required fields
        missing_fields = []
        for field_name, value in [
            ('address', address),
            ('description', description),
            ('priority', priority),
            ('geography_target', geography_target),
            ('reach_type', reach_type),
            ('seo_plan', seo_plan)
        ]:
            if not value:
                missing_fields.append(field_name)
        if missing_fields:
            return func.HttpResponse(
                json.dumps({'error': f'Missing required fields: {", ".join(missing_fields)}'}),
                mimetype="application/json",
                status_code=400
            )

        # Validate allowed values for priority and reach_type
        allowed_priorities = [
            "I want to drive more traffic",
            "I want to rank for specific keywords",
            "I want to improve local visibility",
            "I want to grow my blog",
            "I want to boost my product or service pages"
        ]
        allowed_reach_types = [
            "Quick Wins",
            "Long term Growth"
        ]
        if priority not in allowed_priorities:
            return func.HttpResponse(
                json.dumps({'error': f"Invalid value for 'priority'. Allowed values: {allowed_priorities}"}),
                mimetype="application/json",
                status_code=400
            )
        if reach_type not in allowed_reach_types:
            return func.HttpResponse(
                json.dumps({'error': f"Invalid value for 'reach_type'. Allowed values: {allowed_reach_types}"}),
                mimetype="application/json",
                status_code=400
            )

        # Generate keywords based on the input
        keywords_response = recommend_keywords(
            address=address,
            description=description,
            priority=priority,
            geography_target=geography_target,
            reach_type=reach_type,
            seo_plan=seo_plan
        )

        if keywords_response:
            return func.HttpResponse(json.dumps(keywords_response), mimetype="application/json", status_code=200)
        else:
            return func.HttpResponse(json.dumps({'error': 'Failed to generate keywords.'}), mimetype="application/json", status_code=500)
    except Exception as e:
        logging.error(f"Exception occurred: {e}")
        return func.HttpResponse(json.dumps({'error': str(e)}), mimetype="application/json", status_code=500)

def recommend_keywords(address, description, priority, geography_target, reach_type, seo_plan):
    max_keywords = 30 if seo_plan.lower() == "premium" else 15
    prompt = f"""
You are an expert SEO strategist. Based on the following client information, recommend a list of highly relevant SEO keywords.

Client Information:
- Address: {address}
- Description: {description}
- Priority: {priority}
- Geography Target: {geography_target}
- Reach Type: {reach_type}
- SEO Plan: {seo_plan}

Instructions:
- Recommend a list of the most effective keywords for this client.
- The keywords should be tailored to the business description and priority.
- Do NOT include any locality, city, state, or country in the keywords. Only use generic or business-specific terms.
- If the plan is 'limited', return a maximum of 15 keywords.
- If the plan is 'premium', return a maximum of 30 keywords.
- Return the keywords as a JSON array under the key 'keywords'.

JSON Output Example:
{{
  "keywords": ["keyword1", "keyword2", ...]
}}
"""
    try:
        messages = [{"role": "user", "content": prompt}]
        result = client.chat.completions.create(
            model="gpt-4.1-mini",
            messages=messages,
            response_format={"type": "json_object"}
        )
        response_json = json.loads(result.choices[0].message.content)
        keywords = response_json.get("keywords", [])
        # Truncate to max_keywords in case the model returns more
        keywords = keywords[:max_keywords]
        return {
            "recommended_keywords": keywords
        }
    except Exception as e:
        logging.error(f"Error generating keywords: {e}")
        return None
