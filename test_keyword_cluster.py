#!/usr/bin/env python3
"""
Test script for the getKeywordCluster Azure Function
"""

import json
import sys
import os

# Add the function directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'getKeywordCluster'))

# Mock Azure Functions request for testing
class MockHttpRequest:
    def __init__(self, json_body):
        self._json_body = json_body
    
    def get_json(self):
        return self._json_body

def test_keyword_cluster():
    """Test the keyword cluster function with sample data"""
    
    # Import the function
    from getKeywordCluster import main
    
    # Test data
    test_payload = {
        "keywords": ["digital marketing agency", "web design"],
        "maxCluster": 3,
        "target": "Local", 
        "locations": ["New York", "Brooklyn"]
    }
    
    # Create mock request
    mock_req = MockHttpRequest(test_payload)
    
    # Call the function
    try:
        response = main(mock_req)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.get_body().decode()}")
        
        if response.status_code == 200:
            result = json.loads(response.get_body().decode())
            print("\nParsed Response:")
            print(json.dumps(result, indent=2))
        
    except Exception as e:
        print(f"Error testing function: {e}")

def test_validation():
    """Test input validation"""
    from getKeywordCluster import main
    
    # Test missing fields
    test_cases = [
        {},  # Empty payload
        {"keywords": []},  # Missing other fields
        {"keywords": ["test"], "maxCluster": "invalid"},  # Invalid type
        {"keywords": ["test"], "maxCluster": 5, "target": "Invalid"},  # Invalid target
    ]
    
    for i, test_case in enumerate(test_cases):
        print(f"\nTest case {i+1}: {test_case}")
        mock_req = MockHttpRequest(test_case)
        response = main(mock_req)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.get_body().decode()}")

if __name__ == "__main__":
    print("Testing getKeywordCluster function...")
    print("=" * 50)
    
    print("\n1. Testing basic functionality:")
    test_keyword_cluster()
    
    print("\n2. Testing validation:")
    test_validation()
