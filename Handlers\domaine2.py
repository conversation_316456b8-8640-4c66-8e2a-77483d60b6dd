import json
import random
import azure.functions as func
from openai import OpenAI

client = OpenAI(
  
  api_key ='********************************************************',
)

selected_domaine_responses = [
"Perfect! Thank you for choosing {selected_domain} as your domain name. I'm glad you're happy with your selection. We're now ready to move on to the next step. Just let me know if there's anything else you need assistance with.",
"Excellent choice! {selected_domain} it is. I'm thrilled that you've found the perfect domain name. Let's proceed to the next step together. If you have any further questions or requests, feel free to ask.",
"Wonderful! You've selected {selected_domain} as your domain name. Great decision! Now, let's move forward to the next step. Should you require any additional support or have any concerns, please don't hesitate to let me know.",
"Fantastic! {selected_domain} has been set as your domain name. I'm excited to help you progress to the next stage. If there's anything else you'd like to discuss or if you need any further assistance, just say the word.",
"Congratulations on choosing {selected_domain}! That's a brilliant domain name. Now, let's take the next step together. If there's anything specific you'd like to address or if you need any further guidance, please feel free to share.",
"Marvelous choice! {selected_domain} is now confirmed as your domain name. I'm thrilled to guide you through the next step. Should you require any additional information or have any special requests, please don't hesitate to ask.",
"Great news! {selected_domain} is locked in as your domain name. It's a pleasure to accompany you to the next step. If there's anything else you'd like to explore or if you have any questions, I'm here to assist you.",
"Awesome! Your domain name, {selected_domain}, is all set. I'm excited to help you proceed to the next stage. If there's anything specific you'd like to discuss or if you need any further support, please let me know.",
"Well done on selecting {selected_domain} as your domain name! I'm delighted to be part of your journey to the next step. If there's anything else you'd like to cover or if you require any further assistance, feel free to reach out.",
"Brilliant choice! {selected_domain} is now officially yours. Let's advance to the next step together. Should you have any additional inquiries or if there's anything else I can do to help, please don't hesitate to inform me."
]


def format_domain(domain):
    if not domain :
        return None
    # Remove spaces
    domain = domain.replace(" ", "")

    # Remove "www" if present
    domain = domain.replace("www.", "")

    # Remove "http://" or "https://" if present
    if domain.startswith("http://"):
        domain = domain[7:]
    elif domain.startswith("https://"):
        domain = domain[8:]

    # Remove the extension
    parts = domain.split(".")
    main_part = ".".join(parts[:-1])



    if (main_part == "" or main_part == None or main_part == "NULL" or main_part == "null" or main_part == "null"):
        return None
    return main_part.lower()

def validate_optional_fields(data):
    # optional_fields = ['personName', 'businessName', 'businessDescription', 'wisheddomain', 'olddomain']
    # invalid_fields = [field for field in optional_fields if field in data and not isinstance(data[field], str)]

    # if 'variables' in data and not isinstance(data['variables'], list):
    #     invalid_fields.append('variables')

    # if invalid_fields:
    #     return func.HttpResponse(json.dumps({'error': f"Invalid data for optional field(s): {', '.join(invalid_fields)}"}), 400

    return None
def update_state(state, question):
    """
    Update the conversation state based on the user's response.
    Uses OpenAI to understand the context of the question, categorize it, and extract the relevant information.
    Expects a JSON response with keys 'category' and 'value'.
    """
    def generate_domain_suggestions(state):
        """
        Generate domain name suggestions using OpenAI's API based on business name and description.
        """
        business_name = state['businessName']
        business_description = state['businessDescription']
        user_input = state['question']

        if state["variables"] and state["variables"] != []:
            suggestions = "different from previous suggestions : "+", ".join(state["variables"] )
            suggestions += "/n Note :  only if there are  previous suggestions given it means user is'nt satisfied with previous ones , so display a proper response to appologies with category other"
        else:
            suggestions = ""

        if state["wisheddomain"] and state["wisheddomain"] != '':
            additional_infos = state["wisheddomain"]
        else:
            additional_infos = "no additional infos"

        messages = [{"role": "system",
                "content": "i will suggest creative domain names without extension based on business informations or stick with user given domain suggestion"},
                {"role": "user", "content": f"""based on the given information about a previous conversation an taking notes of user recommendations,
    suggest three small relevant domain names respecting SEO recommendations '{suggestions}' in json with array suggestions.
    ------------
    last user input : '{user_input}'
    business Name : '{business_name}'
    and or business Description : '{business_description}'
    additional infos : {additional_infos}
    ------------
    example of json array :
    {{"suggestions": ["suggestion1","suggestion2","suggestion3"], "response" : "suggest a response to told user to pick the domain he liked from a list of available domains generated" }}
    ------------
    Note :  consider being friendly and speak as a real person ..
    NOte : you may consider iving a domaine suggestion as the same name of the business without any creativity if the user want
    Response in JSON format:"""}]
        try:
            response = client.chat.completions.create(
                model="gpt-4.1-mini",
                messages=messages,
                response_format={"type": "json_object"}

            )
            suggestions = json.loads(response.choices[0].message.content.strip())


            debug_message = {
                'prompt': messages,
                'result': suggestions
            }
            state['debug_messages'].append(debug_message)



            return (suggestions["response"],[suggestion for suggestion in suggestions["suggestions"] if suggestion])  # Filter out empty suggestions
        except Exception as e:
            print(f"Error in generating domain suggestions: {e}")


        return ["Error generating suggestions. Please try again."]
    
    def format_conversation_log(state):
        # Specified keys to check in the state
        keys_to_check = ['personName', 'businessName', 'businessDescription', 'wisheddomain']

        # Check if none of the specified keys are in the state
        if not any(state[key] for key in keys_to_check):
            return ""

        # Filter the dictionary to include all specified keys
        filtered_state = {key: state.get(key, None) for key in keys_to_check}

        previous_data_parts = []
        first_empty_key = None

        for key, value in filtered_state.items():
            conv_question = f"What is your {key.replace('Name', ' name').replace('Description', ' description').replace('domain', ' domain')}?"
            if value:
                previous_data_parts.append(f"System: {conv_question}\nUser: {value}")
            else:
                if not first_empty_key:
                    first_empty_key = key

        # Adding the first empty element as the last message from the system
        if first_empty_key:
            empty_question = f"What is your {first_empty_key.replace('Name', ' name').replace('Description', ' description').replace('domain', ' domain')}?"
            previous_data_parts.append(f"System: {empty_question}")

        # Check if there are no conversation parts
        if not previous_data_parts:
            return ""

        return "\nConversation log:\n" + "\n".join(previous_data_parts)
    
    conv_log = format_conversation_log(state)

    messages = [
        {"role":"system","content":"i'm an agent that whant to help users get a domain suggestions for their businesses but first i'll need to ask them som questions and interact with them until i got all necessary information for domain suggestion"}, {"role": "user", "content": """
Extract relevant values from the input for categories 'person_name', 'business_name', 'business_description' or 'wanted_domain' or other if no information found.
{conv_log}
User Input: '{question}'
-----------------
Tasks :
parse user input in the correspendant category
Suggest a friendly short response as an human agent to display to the user to ask him for rest of informations.
Note that only 'business_name' and 'business_description' are mandatory to generate suggestions.
Ask 'additional_infos' like old domain or wished domain for better suggestions but it's optional.
if no values found skip that category

Provide an example JSON response with suggested domains.
-----------------
Example of json response as a list of json objects :
{"extracted_categories":[{"category":"", "value":""}
...],
"response":"",
Response in JSON format:""".replace("{question}",question).replace("{conv_log}",conv_log)}]

    # try:
    # Using OpenAI to analyze and categorize the question
    response = client.chat.completions.create(
        model="gpt-4.1-mini",
        messages=messages,
        response_format={"type": "json_object"}
    )
    analysis = json.loads(response.choices[0].message.content.strip())

    # Append debug information to the debug_messages list
    debug_message = {
        'prompt': messages,
        'result': analysis
    }
    state['debug_messages'].append(debug_message)

    # Update state based on the analysis
    for item in analysis["extracted_categories"]:
        category = item.get('category', None)
        value = item.get('value', None)


        if 'person_name' in category and not state['personName']:
            state['personName'] = value

        elif 'business_name' in category and not state['businessName']:
            state['businessName'] = value

        elif 'business_description' in category and not state['businessDescription']:
            state['businessDescription'] = value

        elif 'wanted_domain' in category and not state['wisheddomain']:
            state['wisheddomain'] = value

        elif 'other' in category:
            state['value'] = None  # Suggested response for the user

    state['response'] = analysis["response"]  # Suggested response for the user


    if( (state['businessName'] and state['businessDescription']) or state['wisheddomain'] ):
        state['question'] = question
        state['response'],new_variables = generate_domain_suggestions(state)

        if not state['variables']:
            state['variables'] = []
        state['variables'].append(new_variables[0])
    # except Exception as e:
    #     print(f"Error in analyzing and categorizing question: {e}")



    return state

def handle_get_domain_variables2(data):

    error_response = validate_optional_fields(data)
    if error_response:
        return error_response

    question = data.get('question', '')
    state = {
        'question': data['question'],
        'context': data['context'],
        'iteration': data.get('iteration', 0),
        'response': None,
        'personName': data.get('personName', None),
        'businessName': data.get('businessName', None),
        'businessDescription': data.get('businessDescription', None),
        'wisheddomain': data.get('wisheddomain', None),
        'variables': data.get('variables', []),
        'debug_messages': []
    }

    state = update_state(state, question)
    # Prepare the output
    output = {
        'question': state['question'],
        'context': state['context'],
        "iteration": state['iteration']+1,
        'response': state['response'],
        'personName': state['personName'],
        'businessName': state['businessName'],
        'businessDescription': state['businessDescription'],
        'wisheddomain': state['wisheddomain'],
        'variables': state['variables'],
    }
        # "debug_messages": state.get('debug_messages', {})

    return func.HttpResponse(json.dumps(output), mimetype="application/json", status_code=200)


def handle_save_domain_variables2(data):

    error_response = validate_optional_fields(data)
    if error_response:
        return error_response

    question = data['question']
    context = data['context']
    iteration = data['iteration']
    variables = data.get('variables', [])
    person_name = data.get('personName', 'NULL')
    business_name = data.get('businessName', 'NULL')
    business_description = data.get('businessDescription', 'NULL')
    wished_domain = data.get('wisheddomain', 'NULL')
    old_domain = data.get('olddomain', 'NULL')

    if variables == None:
        variables = []

    if len(variables) == 0:
        func.HttpResponse(json.dumps({'error': f"No selected domain found"}), status_code = 400)


    selected_domain = variables[-1]
    response = random.choice(selected_domaine_responses).replace("{selected_domain}",selected_domain)



    additional_fields = {
        'personName': person_name,
        'businessName': business_name,
        'businessDescription': business_description,
        'variables': variables,
        'response': response,
        'iteration': iteration+1,
        'context':context,
        'question':question
    }



    return func.HttpResponse(json.dumps(additional_fields), mimetype="application/json", status_code=200)
