

import logging
import json
import azure.functions as func
from pydantic import BaseModel
from QnAHandlers.MainHandler import *
from QnAHandlers.ORM import *
from QnAHandlers.QAGenerator import *
import azure.functions as func
import logging
import json
from bs4 import BeautifulSoup
import re

def clean_html_content(html_content: str) -> str:
    """
    Clean HTML content by removing tags and preserving meaningful text.
    
    Args:
        html_content (str): Raw HTML content
        
    Returns:
        str: Cleaned text content with preserved spacing and structure
    """
    # Create BeautifulSoup object
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Remove script and style elements
    for script in soup(["script", "style"]):
        script.decompose()
    
    # Get text content
    text = soup.get_text()
    
    # Clean up whitespace
    lines = (line.strip() for line in text.splitlines())
    chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
    text = ' '.join(chunk for chunk in chunks if chunk)
    
    # Remove multiple spaces
    text = re.sub(r'\s+', ' ', text)
    
    # Remove any remaining HTML entities
    text = re.sub(r'&[a-zA-Z]+;', '', text)
    
    return text.strip()

def process_page_content(page_data: dict) -> dict:
    """
    Process page data by cleaning the HTML content.
    
    Args:
        page_data (dict): Dictionary containing page information with HTML content
        
    Returns:
        dict: Updated dictionary with cleaned content
    """
    if 'content' in page_data:
        page_data['content'] = clean_html_content(page_data['content'])
    return page_data



async def main(req: func.HttpRequest) -> func.HttpResponse:
    """Question answering endpoint"""
    try:
        req_body = req.get_json()
        mode = req.params.get('mode', 'QNA')  # QNA or AGENT
        
        # Extract context from request body if provided
        user_context = req_body.get('context', [])
        
        question_request = QuestionRequest(**req_body)

        processor = DataProcessor()
        
        # Get similar content
        similar_content = await processor.embeddings_processor.get_similar_content(
            question_request.question, 
            question_request.clientId
        )
        
        if not similar_content:
            return func.HttpResponse(
                json.dumps({
                    "answer": "I don't have enough information to answer this question.",
                    "noAnswer": True,
                    "source": None,
                    "noSimilarContent": True,
                    "clientId": question_request.clientId
                }),
                mimetype="application/json"
            )
        
        # Load content data
        data = await processor.load_processed_data(question_request.clientId)
        context = []
        
        # Add similar content from database
        for item in similar_content:
            if item['type'] == 'pages':
                content = next((x for x in data['pages'] if str(x['id']) == str(item['id'])), None)
                if content:
                    cleaned_content = clean_html_content(content['content'])
                    context.append(f"Page '{content['name']}': {cleaned_content}")
            elif item['type'] == 'catalogs':
                content = next((x for x in data['catalogs'] if str(x['id']) == str(item['id'])), None)
                if content:
                    context.append(f"{content['type'].capitalize()} '{content['name']}': {content['content']}")
            elif item['type'] == 'qa_pairs':
                content = next((x for x in data['qa_pairs'] if str(x['id']) == str(item['id'])), None)
                if content:
                    context.append(f"Q: {content['question']}\nA: {content['answer']}")

        # Generate answer using GPT-4
        if mode == "AGENT":
            main_prompt = """You are a friendly and helpful customer service agent. Your role is to assist users by leveraging the provided context while maintaining a conversational tone. Follow these guidelines:

            1. Be conversational and engaging while remaining professional:
            - Use a friendly, helpful tone
            - Ask clarifying questions when needed
            - Guide users towards specific topics available in the context
            - Acknowledge user's questions even when you can't fully answer them

            2. When answering questions:
            - Use information from the context but present it conversationally
            - If information is partial, share what you know and suggest related topics
            - When you can't answer, suggest topics from the context they might be interested in
            - Always maintain context relevance while being helpful

            Your response must be a valid JSON object with the following structure:
            {
                "answer": "Your conversational response here",
                "noAnswer": boolean (true if you cannot find relevant information, false if you can answer),
                "source": "Specify which part of the context you used or null if no source"
            }

            Even when you can't fully answer, provide helpful guidance:
            {
                "answer": "I understand you're asking about [topic]. While I don't have that specific information, I can tell you about [related available topics]. Would you like to know more about any of those?",
                "noAnswer": true,
                "source": null
            }"""
        else:
            main_prompt = """You are a helpful assistant. Answer the question based on the provided context. 
                Your response must be a valid JSON object with the following structure:
                {
                    "answer": "Your detailed answer here",
                    "noAnswer": boolean (true if you cannot find relevant information, false if you can answer),
                    "source": "Specify which part of the context you used (e.g., 'Page: About Us', 'Catalog: Products', or 'QA: Customer Service') or null if no source"
                }

                Example response for when you can answer:
                {
                    "answer": "The company was founded in 1995 by John Smith.",
                    "noAnswer": false,
                    "source": "Page: Company History"
                }

                Example response for when you cannot answer:
                {
                    "answer": "I don't have enough information to answer this question.",
                    "noAnswer": true,
                    "source": null
                }

                Always ensure your response is properly formatted JSON."""

        # Prepare context string with both database content and user context
        full_context = context.copy()
        if user_context:
            for qa_pair in user_context:
                if qa_pair.get('question') and qa_pair.get('answer'):
                    full_context.append(f"Q: {qa_pair['question']}\nA: {qa_pair['answer']}")

        messages = [
            {
                "role": "system",
                "content": main_prompt
            },
            {
                "role": "user",
                "content": f"Context:\n{'-' * 80}\n" + "\n\n".join(full_context) + f"\n{'-' * 80}\n\nQuestion: {question_request.question}"
            }
        ]
        
        response = await processor.openai_client.chat.completions.create(
            model=Config.LLM_MODEL,
            messages=messages,
            response_format={"type": "json_object"}
        )
        
        answer_content = json.loads(response.choices[0].message.content)
        
        answer_response = AnswerResponse(
            answer=answer_content.get("answer", ""),
            noAnswer=answer_content.get("noAnswer", True),
            source=answer_content.get("source", None),
            clientId=question_request.clientId
        )
        
        return func.HttpResponse(
            json.dumps(answer_response.__dict__),
            mimetype="application/json"
        )
        
    except Exception as e:
        logging.error(f"Error processing question: {str(e)}")
        return func.HttpResponse(
            json.dumps({
                "error": str(e),
                "answer": "An error occurred while processing your question.",
                "noAnswer": True,
                "source": None,
                "clientId": question_request.clientId if 'question_request' in locals() else None
            }),
            status_code=500,
            mimetype="application/json"
        )
