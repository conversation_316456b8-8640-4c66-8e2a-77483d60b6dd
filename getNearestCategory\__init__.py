import json
import logging
import azure.functions as func
from openai import OpenAI
from openai import AzureOpenAI


client = AzureOpenAI(
    api_key = "********************************",  
    api_version = "2024-08-01-preview",
    azure_endpoint = "https://slicky-open-ai.openai.azure.com/openai/deployments/gpt-4o-mini/chat/completions?api-version=2024-08-01-preview"
)
# Define the categories and their descriptions
CATEGORIES = {
    "1Page": "A small business website that may include both services and products but with very small e-commerce functionality",
    "Ecommerce": "An e-commerce website that sells many products (10 or more)",
    "Small Ecommerce": "An e-commerce website that sells between 2-9 products",
    "1 Ecommerce": "An e-commerce website that sells exactly 1 product",
    "Service": "A website that provides between 2-6 services",
    "1 Service": "A website that provides exactly 1 service",
    "Portfolio": "A website primarily used to display a portfolio of work",
    "Educational": "A website dedicated to teaching or educational content",
    "Personal": "A website for personal use, not business-related",
    "Blogger": "A website primarily for blogging purposes"
}

def classify_website(description, is_business):
    try:
        business_context = "This is a business website." if is_business else "This is not a business website."
        
        # Construct the prompt for GPT-4
        prompt = f"""Given the following website description, classify it into EXACTLY ONE of these specific categories:
Important Context: {business_context}

Categories:
- 1Page: Small business website, may include both services and products but very small e-commerce
- Ecommerce: Sells 10+ products
- Small Ecommerce: Sells 2-9 products
- 1 Ecommerce: Sells exactly 1 product
- Service: Provides 2-6 services
- 1 Service: Provides exactly 1 service
- Portfolio: Display portfolio only
- Educational: Teaching content
- Personal: Personal use, not business
- Blogger: Blogs

Website description: {description}

Return ONLY the category name, nothing else. Choose from the exact categories listed above."""

        # Get classification from GPT-4
        response = client.chat.completions.create(
            model="gpt-4o-mini",  # Use appropriate model ID for GPT-4-mini
            messages=[
                {"role": "system", "content": "You are a website classifier. Respond only with the exact category name, nothing else."},
                {"role": "user", "content": prompt}
            ],
            temperature=0,
            max_tokens=10
        )

        # Extract the category
        category = response.choices[0].message.content.strip()
        
        # Validate that the returned category is in our defined categories
        if category not in CATEGORIES:
            return {"error": "Invalid category returned", "category": None}
            
        return {"category": category}

    except Exception as e:
        logging.error(f"Error in classification: {str(e)}")
        return {"error": str(e), "category": None}

def generate_fake_content(description, category, content_total):
    try:
        # Updated prompt to allow for mixed content types and include type field
        prompt = f"""Analyze the following business description and determine whether this business offers PRODUCTS, SERVICES, or BOTH.

Business description: {description}
Business category: {category}

Based on your analysis, generate {content_total} realistic items for this business. If the business would likely offer both products and services, include exactly {content_total} products AND {content_total} services (for a total of {content_total*2} items).

For each item, provide:
1. Type (either "product" or "service")
2. Name
3. Short description (1-2 sentences)
4. Long description (3-4 sentences)
5. Price (MUST be an integer value only, no decimal points, no currency symbols)
6. Image keyword (a keyword for searching an image on Unsplash, one word only if the keywords would not be relevant consider use like the industry name to be sure we will have accurate image at least with same theme)
"""

        # Get content generation using JSON response format
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a business content generator. Determine if the business sells products, services, or both based on the description, then generate appropriate content. If both products and services apply, generate an equal number of each. Return a JSON object with a 'fakeContent' array containing objects with keys: 'type', 'name', 'short_description', 'long_description', 'price', and 'image_keyword'. The 'type' field should be either 'product' or 'service'."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.7,
            response_format={"type": "json_object"}
        )

        # Get the content directly as JSON
        content_json = json.loads(response.choices[0].message.content)
        
        # Ensure the response contains the expected structure
        if "fakeContent" not in content_json:
            # If the model didn't use our exact key format, try to extract the content array
            # Look for any key that might contain a list of items
            for key, value in content_json.items():
                if isinstance(value, list) and len(value) > 0:
                    # Add the type field if it's missing
                    for item in value:
                        if "type" not in item:
                            # Try to infer type based on other fields or default to "product"
                            item["type"] = "product"
                    return {"fakeContent": value}
            
            # If we couldn't find a suitable array, return an error
            logging.error(f"Unexpected JSON structure: {content_json}")
            return {"error": "Invalid content structure returned", "fakeContent": []}
        
        # Ensure all items have a type field and convert to numeric values
        for item in content_json["fakeContent"]:
            if "type" not in item:
                # Try to infer type based on other fields or default to "product"
                item["type"] = "product"
            
            # Convert type strings to numeric values (0 for products, 1 for services)
            if isinstance(item["type"], str):
                item_type = item["type"].lower().strip()
                if "service" in item_type:
                    item["type"] = 1
                else:  # Default to product
                    item["type"] = 0
                
        return {"fakeContent": content_json["fakeContent"]}

    except Exception as e:
        logging.error(f"Error in content generation: {str(e)}")
        return {"error": str(e), "fakeContent": []}

def generate_product_categories(description, category, num_categories=5):
    try:
        # Create prompt for generating product categories
        prompt = f"""Analyze the following business description and generate {num_categories} product categories that would be relevant for this business.

Business description: {description}
Business category: {category}

For each category, provide:
1. Name (concise category name)
2. Image (a single word that describes this category - will be used as an image search keyword)

Example for a grocery store:
[
  {{"name": "Dairy", "image": "milk"}},
  {{"name": "Produce", "image": "vegetables"}},
  ...
]

Return only JSON with an array called 'fakeCategories' containing objects with 'name' and 'image' keys.
"""

        # Get category generation using JSON response format
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a business category generator. Generate realistic product categories based on the business description. Return a JSON object with a 'fakeCategories' array containing objects with keys: 'name' and 'image'. The 'image' field should be a single word that best represents the category for image search."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.5,
            response_format={"type": "json_object"}
        )

        # Get the content directly as JSON
        content_json = json.loads(response.choices[0].message.content)
        
        # Ensure the response contains the expected structure
        if "fakeCategories" not in content_json:
            # If the model didn't use our exact key format, try to extract the content array
            for key, value in content_json.items():
                if isinstance(value, list) and len(value) > 0:
                    # Check if the items have name and image
                    all_valid = True
                    for item in value:
                        if "name" not in item or "image" not in item:
                            all_valid = False
                            break
                    
                    if all_valid:
                        # Convert to the correct structure with type 2
                        categories = []
                        for item in value:
                            categories.append({
                                "type": 2,
                                "name": item["name"],
                                "image": item["image"]
                            })
                        return {"categories": categories}
            
            # If we couldn't find a suitable array, return an error
            logging.error(f"Unexpected JSON structure: {content_json}")
            return {"error": "Invalid category structure returned", "categories": []}
        
        # Convert the categories to have type 2
        categories = []
        for item in content_json["fakeCategories"]:
            categories.append({
                "type": 2,
                "name": item["name"],
                "image": item["image"]
            })
                
        return {"categories": categories}

    except Exception as e:
        logging.error(f"Error in category generation: {str(e)}")
        return {"error": str(e), "categories": []}


def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Python HTTP trigger function processed a request.')

    try:
        # Ensure request method is POST
        if req.method != "POST":
            return func.HttpResponse(
                json.dumps({"error": "Only POST method is allowed"}),
                mimetype="application/json",
                status_code=405
            )

        # Get request body
        try:
            req_body = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps({"error": "Invalid JSON in request body"}),
                mimetype="application/json",
                status_code=400
            )

        # Extract parameters
        description = req_body.get('description')
        is_business = req_body.get('isBusiness')
        generate_content = req_body.get('generateContent', False)
        generate_categories = req_body.get('generateCategories', False)
        content_total = req_body.get('contentTotal', 3)  # Default to 3 if not provided
        category_total = req_body.get('categoryTotal', 5)  # Default to 5 if not provided

        # Validate required parameters
        if description is None:
            return func.HttpResponse(
                json.dumps({"error": "Missing required parameter: description"}),
                mimetype="application/json",
                status_code=400
            )
        if is_business is None:
            return func.HttpResponse(
                json.dumps({"error": "Missing required parameter: isBusiness"}),
                mimetype="application/json",
                status_code=400
            )

        # Validate isBusiness is boolean
        if not isinstance(is_business, bool):
            return func.HttpResponse(
                json.dumps({"error": "Parameter isBusiness must be a boolean"}),
                mimetype="application/json",
                status_code=400
            )
            
        # Validate generateContent is boolean if provided
        if generate_content is not None and not isinstance(generate_content, bool):
            return func.HttpResponse(
                json.dumps({"error": "Parameter generateContent must be a boolean"}),
                mimetype="application/json",
                status_code=400
            )
            
        # Validate contentTotal is integer if provided
        if content_total is not None and not isinstance(content_total, int):
            return func.HttpResponse(
                json.dumps({"error": "Parameter contentTotal must be an integer"}),
                mimetype="application/json",
                status_code=400
            )
            
        # Validate categoryTotal is integer if provided
        if category_total is not None and not isinstance(category_total, int):
            return func.HttpResponse(
                json.dumps({"error": "Parameter categoryTotal must be an integer"}),
                mimetype="application/json",
                status_code=400
            )

        # Get classification
        classification_result = classify_website(description, is_business)
        
        if "error" in classification_result and classification_result["category"] is None:
            return func.HttpResponse(
                json.dumps({"error": classification_result["error"]}),
                mimetype="application/json",
                status_code=500
            )

        category = classification_result["category"]
        response_data = {"category": category}
        
        # Initialize fakeContent array
        fakeContent = []
        
        # Generate fake content if requested
        if generate_content:
            content_result = generate_fake_content(description, category, content_total)
            
            if "error" in content_result:
                logging.warning(f"Content generation error: {content_result['error']}")
            
            fakeContent.extend(content_result.get("fakeContent", []))

        # Generate product categories if requested
        if generate_categories:
            category_result = generate_product_categories(description, category, category_total)
            
            if "error" in category_result:
                logging.warning(f"Category generation error: {category_result['error']}")
            
            # Add categories (type 2) to fakeContent
            fakeContent.extend(category_result.get("categories", []))
        
        # Add fakeContent to response data
        if fakeContent:
            response_data["fakeContent"] = fakeContent

        return func.HttpResponse(
            json.dumps(response_data),
            mimetype="application/json",
            status_code=200
        )

    except Exception as e:
        logging.error(f"Unexpected error: {str(e)}")
        return func.HttpResponse(
            json.dumps({"error": str(e)}),
            mimetype="application/json",
            status_code=500
        )
