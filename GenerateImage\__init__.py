import logging
import json
import azure.functions as func
from pydantic import BaseModel
from openai import OpenAI
import os
from typing import Optional, Literal

class ImageRequest(BaseModel):
    """Request model for image generation"""
    prompt: str
    size: Optional[str] = None  # e.g. "1024x1024", "1536x1024", "auto"
    model: Optional[str] = "gpt-image-1"

class ImageResponse(BaseModel):
    """Response model for image generation"""
    imageUrl: Optional[str] = None
    error: Optional[str] = None

def validate_size(model: str, size: Optional[str]) -> str:
    """
    Validate and adjust the size string based on the model requirements.

    Args:
        model (str): The image model version
        size (str or None): Requested image size

    Returns:
        str: Validated size string

    Raises:
        ValueError: If the size is invalid for the model
    """
    gpt_image_1_sizes = ["1024x1024", "1536x1024", "1024x1536", "auto"]
    dalle2_sizes = ["256x256", "512x512", "1024x1024"]

    if model == "gpt-image-1":
        if size is None:
            return "auto"
        if size not in gpt_image_1_sizes:
            raise ValueError(f"Invalid size for gpt-image-1. Allowed values: {gpt_image_1_sizes}")
        return size
    elif model == "dall-e-2":
        if size is None:
            return "1024x1024"
        if size not in dalle2_sizes:
            raise ValueError(f"Invalid size for dall-e-2. Allowed values: {dalle2_sizes}")
        return size
    elif model == "dall-e-3":
        # Assume only 1024x1024 is supported for dall-e-3
        return "1024x1024"
    else:
        raise ValueError(f"Unsupported model: {model}")

async def main(req: func.HttpRequest) -> func.HttpResponse:
    """Image generation endpoint"""
    try:
        # Parse request body
        req_body = req.get_json()
        # Check for required fields
        if "prompt" not in req_body or not req_body["prompt"]:
            return func.HttpResponse(
                json.dumps({"error": "Missing required field: prompt"}),
                status_code=400,
                mimetype="application/json"
            )
        # Model
        model = req_body.get("model", "gpt-image-1")
        allowed_models = ["gpt-image-1", "dall-e-2", "dall-e-3"]
        if model not in allowed_models:
            return func.HttpResponse(
                json.dumps({"error": f"Invalid model. Allowed values: {allowed_models}"}),
                status_code=400,
                mimetype="application/json"
            )
        # Size
        size = req_body.get("size")
        try:
            validated_size = validate_size(model, size)
        except ValueError as ve:
            return func.HttpResponse(
                json.dumps({"error": str(ve)}),
                status_code=400,
                mimetype="application/json"
            )

        image_request = ImageRequest(prompt=req_body["prompt"], size=validated_size, model=model)

        # Initialize OpenAI client
        client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

        # Generate image
        response = client.images.generate(
            model=image_request.model,
            prompt="Photorealistic, ultra-detailed photograph, natural lighting, crisp focus, detailed textures, 8k quality:" + image_request.prompt,
            n=1,
            size=image_request.size
        )

        # Prepare success response
        image_response = ImageResponse(
            imageUrl=response.data[0].url,
        )

        return func.HttpResponse(
            json.dumps(image_response.dict()),
            mimetype="application/json"
        )

    except Exception as e:
        logging.error(f"Error generating image: {str(e)}")

        # Prepare error response
        error_response = ImageResponse(
            error=str(e),
        )

        return func.HttpResponse(
            json.dumps(error_response.dict()),
            status_code=500,
            mimetype="application/json"
        )
