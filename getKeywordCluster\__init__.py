# Example payload to call this endpoint:
# {
#   "keywords": ["digital marketing agency", "web design", "seo services"],
#   "maxCluster": 5,
#   "target": "Local",
#   "locations": ["New York", "Brooklyn", "Manhattan"]
# }

import azure.functions as func
import json
import logging
from openai import OpenAI

# Initialize the OpenAI client
client = OpenAI(
    api_key='********************************************************',
)

def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Python HTTP trigger function processed a request to generate keyword clusters.')

    try:
        # Parse JSON body
        try:
            req_body = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps({'error': 'Invalid JSON body.'}),
                mimetype="application/json",
                status_code=400
            )

        # Extract required fields
        keywords = req_body.get('keywords')
        max_cluster = req_body.get('maxCluster')
        target = req_body.get('target')
        locations = req_body.get('locations')

        # Validate required fields
        missing_fields = []
        for field_name, value in [
            ('keywords', keywords),
            ('maxCluster', max_cluster),
            ('target', target),
            ('locations', locations)
        ]:
            if value is None:
                missing_fields.append(field_name)

        if missing_fields:
            return func.HttpResponse(
                json.dumps({'error': f'Missing required fields: {", ".join(missing_fields)}'}),
                mimetype="application/json",
                status_code=400
            )

        # Validate field types and values
        if not isinstance(keywords, list) or len(keywords) == 0:
            return func.HttpResponse(
                json.dumps({'error': 'keywords must be a non-empty list of strings'}),
                mimetype="application/json",
                status_code=400
            )

        if not isinstance(max_cluster, int) or max_cluster <= 0:
            return func.HttpResponse(
                json.dumps({'error': 'maxCluster must be a positive integer'}),
                mimetype="application/json",
                status_code=400
            )

        if target not in ['Local', 'National', 'Global']:
            return func.HttpResponse(
                json.dumps({'error': "target must be one of: 'Local', 'National', 'Global'"}),
                mimetype="application/json",
                status_code=400
            )

        if not isinstance(locations, list) or len(locations) == 0:
            return func.HttpResponse(
                json.dumps({'error': 'locations must be a non-empty list of strings'}),
                mimetype="application/json",
                status_code=400
            )

        # Validate that all keywords are strings
        for i, keyword in enumerate(keywords):
            if not isinstance(keyword, str) or not keyword.strip():
                return func.HttpResponse(
                    json.dumps({'error': f'All keywords must be non-empty strings. Invalid keyword at index {i}'}),
                    mimetype="application/json",
                    status_code=400
                )

        # Validate that all locations are strings
        for i, location in enumerate(locations):
            if not isinstance(location, str) or not location.strip():
                return func.HttpResponse(
                    json.dumps({'error': f'All locations must be non-empty strings. Invalid location at index {i}'}),
                    mimetype="application/json",
                    status_code=400
                )

        # Generate keyword clusters
        clusters_response = generate_keyword_clusters(
            keywords=keywords,
            max_cluster=max_cluster,
            target=target,
            locations=locations
        )

        if clusters_response:
            return func.HttpResponse(
                json.dumps(clusters_response),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({'error': 'Failed to generate keyword clusters.'}),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logging.error(f"Exception occurred: {e}")
        return func.HttpResponse(
            json.dumps({'error': str(e)}),
            mimetype="application/json",
            status_code=500
        )

def generate_keyword_clusters(keywords, max_cluster, target, locations):
    """
    Generate keyword clusters using OpenAI GPT-4.1-mini model.

    Args:
        keywords: List of keyword strings
        max_cluster: Maximum number of variations per keyword
        target: Target type ('Local', 'National', 'Global')
        locations: List of location strings

    Returns:
        Dictionary with keyword clusters or None if failed
    """
    try:
        # Create location context based on target type
        location_context = create_location_context(target, locations)

        # Create the prompt for OpenAI
        prompt = create_cluster_prompt(keywords, max_cluster, target, location_context)

        # Call OpenAI API
        messages = [{"role": "user", "content": prompt}]
        result = client.chat.completions.create(
            model="gpt-4o-mini",  # Using gpt-4o-mini as it's more commonly available than gpt-4.1-mini
            messages=messages,
            response_format={"type": "json_object"},
            temperature=0.7  # Slight creativity for variations
        )

        # Parse the response
        response_json = json.loads(result.choices[0].message.content)

        # Validate and format the response
        if "keywords" not in response_json:
            logging.error("OpenAI response missing 'keywords' field")
            return None

        # Ensure each keyword cluster doesn't exceed max_cluster
        formatted_keywords = []
        for keyword_data in response_json["keywords"]:
            if "keyword" in keyword_data and "cluster" in keyword_data:
                cluster = keyword_data["cluster"][:max_cluster]  # Truncate if needed
                formatted_keywords.append({
                    "keyword": keyword_data["keyword"],
                    "cluster": cluster
                })

        return {"keywords": formatted_keywords}

    except Exception as e:
        logging.error(f"Error generating keyword clusters: {e}")
        return None

def create_location_context(target, locations):
    """
    Create location context string based on target type and locations.

    Args:
        target: Target type ('Local', 'National', 'Global')
        locations: List of location strings

    Returns:
        String describing the location context
    """
    location_list = ", ".join(locations)

    if target == "Local":
        return f"Local targeting for cities/areas: {location_list}. Focus on city-level, neighborhood-level, and 'near me' variations."
    elif target == "National":
        return f"National targeting for regions: {location_list}. Focus on state-level, country-level, and regional variations."
    else:  # Global
        return f"Global targeting with awareness of: {location_list}. Focus on international, multilingual, and broad geographic variations."

def create_cluster_prompt(keywords, max_cluster, target, location_context):
    """
    Create the prompt for OpenAI to generate keyword clusters.

    Args:
        keywords: List of keyword strings
        max_cluster: Maximum number of variations per keyword
        target: Target type ('Local', 'National', 'Global')
        location_context: Location context string

    Returns:
        String prompt for OpenAI
    """
    keywords_list = ", ".join([f'"{kw}"' for kw in keywords])

    prompt = f"""You are an expert SEO strategist specializing in keyword clustering and local search optimization.

Your task is to generate smart keyword variations for each provided keyword, creating clusters that are context-aware and optimized for search intent.

KEYWORDS TO PROCESS: {keywords_list}

TARGET TYPE: {target}
LOCATION CONTEXT: {location_context}

INSTRUCTIONS:
1. For each keyword, generate exactly {max_cluster} smart variations
2. Make variations context-aware based on the target type:

   - LOCAL TARGET: Include variations like:
     * "[keyword] near me"
     * "[keyword] in [Location]"
     * "best [keyword] [Location]"
     * "[Location] [keyword]"
     * "local [keyword]"

   - NATIONAL TARGET: Include variations like:
     * "top [keyword] in [Location]"
     * "[keyword] [Location] state/country"
     * "best [keyword] companies [Location]"
     * "[Location] [keyword] services"
     * "leading [keyword] [Location]"

   - GLOBAL TARGET: Include variations like:
     * "international [keyword]"
     * "global [keyword] services"
     * "[keyword] worldwide"
     * "online [keyword]"
     * "[keyword] solutions"

3. Use the [Location] placeholder dynamically - it will be replaced with actual locations
4. Consider search intent: informational, commercial, transactional
5. Include long-tail variations when appropriate
6. Ensure variations are natural and commonly searched
7. Avoid duplicate variations within each cluster

OUTPUT FORMAT (JSON):
{{
  "keywords": [
    {{
      "keyword": "original_keyword_1",
      "cluster": [
        "variation 1",
        "variation 2",
        "variation 3"
      ]
    }},
    {{
      "keyword": "original_keyword_2",
      "cluster": [
        "variation 1",
        "variation 2",
        "variation 3"
      ]
    }}
  ]
}}

Generate smart, SEO-optimized keyword clusters now:"""

    return prompt
