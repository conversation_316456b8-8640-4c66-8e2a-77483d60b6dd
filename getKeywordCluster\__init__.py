# Example payload to call this endpoint:
# {
#   "keywords": ["digital marketing agency", "responsive web design", "seo services"],
#   "maxCluster": 4,
#   "target": "Local",
#   "locations": ["New York", "Brooklyn", "Manhattan"]
# }
#
# Expected output format:
# {
#   "keywords": [
#     {
#       "keyword": "digital marketing agency",
#       "cluster": [
#         "digital marketing agency near me",
#         "best digital marketing agency in {CITY}",
#         "{CITY} digital marketing agency",
#         "digital marketing agency services {CITY}"
#       ]
#     },
#     {
#       "keyword": "responsive web design",
#       "cluster": [
#         "professional responsive web design",
#         "custom responsive web design solutions",
#         "best responsive web design services",
#         "responsive web design experts"
#       ]
#     }
#   ]
# }

import azure.functions as func
import json
import logging
from openai import OpenAI

# Initialize the OpenAI client
client = OpenAI(
    api_key='********************************************************',
)

def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Python HTTP trigger function processed a request to generate keyword clusters.')

    try:
        # Parse JSON body
        try:
            req_body = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps({'error': 'Invalid JSON body.'}),
                mimetype="application/json",
                status_code=400
            )

        # Extract required fields
        keywords = req_body.get('keywords')
        max_cluster = req_body.get('maxCluster')
        target = req_body.get('target')
        locations = req_body.get('locations')

        # Validate required fields
        missing_fields = []
        for field_name, value in [
            ('keywords', keywords),
            ('maxCluster', max_cluster),
            ('target', target),
            ('locations', locations)
        ]:
            if value is None:
                missing_fields.append(field_name)

        if missing_fields:
            return func.HttpResponse(
                json.dumps({'error': f'Missing required fields: {", ".join(missing_fields)}'}),
                mimetype="application/json",
                status_code=400
            )

        # Validate field types and values
        if not isinstance(keywords, list) or len(keywords) == 0:
            return func.HttpResponse(
                json.dumps({'error': 'keywords must be a non-empty list of strings'}),
                mimetype="application/json",
                status_code=400
            )

        if not isinstance(max_cluster, int) or max_cluster <= 0:
            return func.HttpResponse(
                json.dumps({'error': 'maxCluster must be a positive integer'}),
                mimetype="application/json",
                status_code=400
            )

        if target not in ['Local', 'National', 'Global']:
            return func.HttpResponse(
                json.dumps({'error': "target must be one of: 'Local', 'National', 'Global'"}),
                mimetype="application/json",
                status_code=400
            )

        if not isinstance(locations, list) or len(locations) == 0:
            return func.HttpResponse(
                json.dumps({'error': 'locations must be a non-empty list of strings'}),
                mimetype="application/json",
                status_code=400
            )

        # Validate that all keywords are strings
        for i, keyword in enumerate(keywords):
            if not isinstance(keyword, str) or not keyword.strip():
                return func.HttpResponse(
                    json.dumps({'error': f'All keywords must be non-empty strings. Invalid keyword at index {i}'}),
                    mimetype="application/json",
                    status_code=400
                )

        # Validate that all locations are strings
        for i, location in enumerate(locations):
            if not isinstance(location, str) or not location.strip():
                return func.HttpResponse(
                    json.dumps({'error': f'All locations must be non-empty strings. Invalid location at index {i}'}),
                    mimetype="application/json",
                    status_code=400
                )

        # Generate keyword clusters
        clusters_response = generate_keyword_clusters(
            keywords=keywords,
            max_cluster=max_cluster,
            target=target,
            locations=locations
        )

        if clusters_response:
            return func.HttpResponse(
                json.dumps(clusters_response),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({'error': 'Failed to generate keyword clusters.'}),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logging.error(f"Exception occurred: {e}")
        return func.HttpResponse(
            json.dumps({'error': str(e)}),
            mimetype="application/json",
            status_code=500
        )

def generate_keyword_clusters(keywords, max_cluster, target, locations):
    """
    Generate keyword clusters using OpenAI GPT-4.1-mini model.

    Args:
        keywords: List of keyword strings
        max_cluster: Maximum number of variations per keyword
        target: Target type ('Local', 'National', 'Global')
        locations: List of location strings

    Returns:
        Dictionary with keyword clusters or None if failed
    """
    try:
        # Create location context based on target type
        location_context = create_location_context(target, locations)

        # Create the prompt for OpenAI
        prompt = create_cluster_prompt(keywords, max_cluster, target, location_context)

        # Call OpenAI API
        messages = [{"role": "user", "content": prompt}]
        result = client.chat.completions.create(
            model="gpt-4o-mini",  # Using gpt-4o-mini as it's more commonly available than gpt-4.1-mini
            messages=messages,
            response_format={"type": "json_object"},
            temperature=0.7  # Slight creativity for variations
        )

        # Parse the response
        response_json = json.loads(result.choices[0].message.content)

        # Validate and format the response
        if "keywords" not in response_json:
            logging.error("OpenAI response missing 'keywords' field")
            return None

        # Ensure each keyword cluster doesn't exceed max_cluster
        formatted_keywords = []
        for keyword_data in response_json["keywords"]:
            if "keyword" in keyword_data and "cluster" in keyword_data:
                cluster = keyword_data["cluster"][:max_cluster]  # Truncate if needed
                formatted_keywords.append({
                    "keyword": keyword_data["keyword"],
                    "cluster": cluster
                })

        return {"keywords": formatted_keywords}

    except Exception as e:
        logging.error(f"Error generating keyword clusters: {e}")
        return None

def create_location_context(target, locations):
    """
    Create location context string based on target type and locations.

    Args:
        target: Target type ('Local', 'National', 'Global')
        locations: List of location strings

    Returns:
        String describing the location context
    """
    location_list = ", ".join(locations)

    if target == "Local":
        return f"Target cities/areas: {location_list}. The {{CITY}} variable will be replaced with these specific cities. Focus on local service intent and 'near me' searches."
    elif target == "National":
        return f"Target states/regions: {location_list}. The {{STATE}} variable will be replaced with these regions. Focus on regional and state-level searches."
    else:  # Global
        return f"Target countries/regions: {location_list}. The {{COUNTRY}} variable will be replaced with these locations. Focus on international and broad geographic searches."

def create_cluster_prompt(keywords, max_cluster, target, location_context):
    """
    Create the prompt for OpenAI to generate keyword clusters.

    Args:
        keywords: List of keyword strings
        max_cluster: Maximum number of variations per keyword
        target: Target type ('Local', 'National', 'Global')
        location_context: Location context string

    Returns:
        String prompt for OpenAI
    """
    keywords_list = ", ".join([f'"{kw}"' for kw in keywords])

    # Define location variables based on target type
    if target == "Local":
        location_var = "{CITY}"
        location_examples = "New York, Brooklyn, Manhattan"
    elif target == "National":
        location_var = "{STATE}"
        location_examples = "California, Texas, New York State"
    else:  # Global
        location_var = "{COUNTRY}"
        location_examples = "United States, Canada, United Kingdom"

    prompt = f"""You are an expert SEO strategist specializing in keyword clustering and intelligent search optimization.

Your task is to generate smart, realistic keyword variations for each provided keyword. You must analyze each keyword to determine if it's a SERVICE or a SPECIALTY/PRODUCT and generate appropriate variations.

KEYWORDS TO PROCESS: {keywords_list}

TARGET TYPE: {target}
LOCATION CONTEXT: {location_context}
LOCATION VARIABLE: Use {location_var} as placeholder (examples: {location_examples})

CRITICAL INSTRUCTIONS:

1. ANALYZE EACH KEYWORD TYPE:
   - SERVICE KEYWORDS (agencies, consultants, contractors, repair, cleaning, etc.): Use location-based patterns
   - SPECIALTY/PRODUCT KEYWORDS (specific products, techniques, materials, etc.): Focus on descriptive variations

2. FOR SERVICE KEYWORDS - Generate realistic location-based variations:
   - "{location_var} [keyword]"
   - "[keyword] in {location_var}"
   - "[keyword] near me"
   - "best [keyword] {location_var}"
   - "top [keyword] {location_var}"
   - "[keyword] services {location_var}"

3. FOR SPECIALTY/PRODUCT KEYWORDS - Generate descriptive, realistic variations:
   - "best [keyword]"
   - "[keyword] solutions"
   - "professional [keyword]"
   - "[keyword] experts"
   - "custom [keyword]"
   - "[keyword] specialists"
   - "affordable [keyword]"
   - "premium [keyword]"

4. TARGET-SPECIFIC ADAPTATIONS:
   - LOCAL: Focus on immediate area, "near me", neighborhood-level
   - NATIONAL: Focus on state/region level, "top in {location_var}"
   - GLOBAL: Focus on broader terms, "international", "worldwide"

5. GENERATE EXACTLY {max_cluster} variations per keyword
6. Make variations natural and commonly searched
7. Avoid repetitive patterns within each cluster
8. Consider different search intents: informational, commercial, transactional

EXAMPLES:

For "digital marketing agency" (SERVICE):
- "digital marketing agency near me"
- "best digital marketing agency in {location_var}"
- "{location_var} digital marketing agency"

For "responsive web design" (SPECIALTY):
- "professional responsive web design"
- "custom responsive web design solutions"
- "best responsive web design services"

OUTPUT FORMAT (JSON):
{{
  "keywords": [
    {{
      "keyword": "original_keyword_1",
      "cluster": [
        "realistic variation 1",
        "realistic variation 2",
        "realistic variation 3"
      ]
    }}
  ]
}}

Analyze each keyword intelligently and generate realistic, search-optimized variations now:"""

    return prompt
