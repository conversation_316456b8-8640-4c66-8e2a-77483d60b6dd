# Example payload to call this endpoint:
# {
#   "keywords": ["digital marketing agency", "responsive web design", "seo services"],
#   "maxCluster": 4,
#   "target": "Local",
#   "locations": ["New York", "Brooklyn", "Manhattan"]
# }
#
# Expected output format:
# {
#   "keywords": [
#     {
#       "keyword": "digital marketing agency",
#       "cluster": [
#         "digital marketing agency near me",
#         "best digital marketing agency in New York",
#         "Brooklyn digital marketing agency",
#         "digital marketing agency services Manhattan"
#       ]
#     },
#     {
#       "keyword": "responsive web design",
#       "cluster": [
#         "professional responsive web design",
#         "custom responsive web design solutions",
#         "best responsive web design services",
#         "responsive web design experts"
#       ]
#     }
#   ]
# }

import azure.functions as func
import json
import logging
from openai import OpenAI

# Initialize the OpenAI client
client = OpenAI(
    api_key='********************************************************',
)

def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Python HTTP trigger function processed a request to generate keyword clusters.')

    try:
        # Parse JSON body
        try:
            req_body = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps({'error': 'Invalid JSON body.'}),
                mimetype="application/json",
                status_code=400
            )

        # Extract required fields
        keywords = req_body.get('keywords')
        max_cluster = req_body.get('maxCluster')
        target = req_body.get('target')
        locations = req_body.get('locations')

        # Validate required fields
        missing_fields = []
        for field_name, value in [
            ('keywords', keywords),
            ('maxCluster', max_cluster),
            ('target', target),
            ('locations', locations)
        ]:
            if value is None:
                missing_fields.append(field_name)

        if missing_fields:
            return func.HttpResponse(
                json.dumps({'error': f'Missing required fields: {", ".join(missing_fields)}'}),
                mimetype="application/json",
                status_code=400
            )

        # Validate field types and values
        if not isinstance(keywords, list) or len(keywords) == 0:
            return func.HttpResponse(
                json.dumps({'error': 'keywords must be a non-empty list of strings'}),
                mimetype="application/json",
                status_code=400
            )

        if not isinstance(max_cluster, int) or max_cluster <= 0:
            return func.HttpResponse(
                json.dumps({'error': 'maxCluster must be a positive integer'}),
                mimetype="application/json",
                status_code=400
            )

        if target not in ['Local', 'National', 'Global']:
            return func.HttpResponse(
                json.dumps({'error': "target must be one of: 'Local', 'National', 'Global'"}),
                mimetype="application/json",
                status_code=400
            )

        if not isinstance(locations, list) or len(locations) == 0:
            return func.HttpResponse(
                json.dumps({'error': 'locations must be a non-empty list of strings'}),
                mimetype="application/json",
                status_code=400
            )

        # Validate that all keywords are strings
        for i, keyword in enumerate(keywords):
            if not isinstance(keyword, str) or not keyword.strip():
                return func.HttpResponse(
                    json.dumps({'error': f'All keywords must be non-empty strings. Invalid keyword at index {i}'}),
                    mimetype="application/json",
                    status_code=400
                )

        # Validate that all locations are strings
        for i, location in enumerate(locations):
            if not isinstance(location, str) or not location.strip():
                return func.HttpResponse(
                    json.dumps({'error': f'All locations must be non-empty strings. Invalid location at index {i}'}),
                    mimetype="application/json",
                    status_code=400
                )

        # Generate keyword clusters
        clusters_response = generate_keyword_clusters(
            keywords=keywords,
            max_cluster=max_cluster,
            target=target,
            locations=locations
        )

        if clusters_response:
            return func.HttpResponse(
                json.dumps(clusters_response),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({'error': 'Failed to generate keyword clusters.'}),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logging.error(f"Exception occurred: {e}")
        return func.HttpResponse(
            json.dumps({'error': str(e)}),
            mimetype="application/json",
            status_code=500
        )

def generate_keyword_clusters(keywords, max_cluster, target, locations):
    """
    Generate keyword clusters using OpenAI GPT-4.1-mini model.

    Args:
        keywords: List of keyword strings
        max_cluster: Maximum number of variations per keyword
        target: Target type ('Local', 'National', 'Global')
        locations: List of location strings

    Returns:
        Dictionary with keyword clusters or None if failed
    """
    try:
        # Create the prompt for OpenAI
        prompt = create_cluster_prompt(keywords, max_cluster, target, locations)

        # Call OpenAI API
        messages = [{"role": "user", "content": prompt}]
        result = client.chat.completions.create(
            model="gpt-4o-mini",  # Using gpt-4o-mini as it's more commonly available than gpt-4.1-mini
            messages=messages,
            response_format={"type": "json_object"},
            temperature=0.7  # Slight creativity for variations
        )

        # Parse the response
        response_json = json.loads(result.choices[0].message.content)

        # Validate and format the response
        if "keywords" not in response_json:
            logging.error("OpenAI response missing 'keywords' field")
            return None

        # Ensure each keyword cluster doesn't exceed max_cluster
        formatted_keywords = []
        for keyword_data in response_json["keywords"]:
            if "keyword" in keyword_data and "cluster" in keyword_data:
                cluster = keyword_data["cluster"][:max_cluster]  # Truncate if needed
                formatted_keywords.append({
                    "keyword": keyword_data["keyword"],
                    "cluster": cluster
                })

        return {"keywords": formatted_keywords}

    except Exception as e:
        logging.error(f"Error generating keyword clusters: {e}")
        return None



def create_cluster_prompt(keywords, max_cluster, target, locations):
    """
    Create the prompt for OpenAI to generate keyword clusters.

    Args:
        keywords: List of keyword strings
        max_cluster: Maximum number of variations per keyword
        target: Target type ('Local', 'National', 'Global')
        locations: List of location strings

    Returns:
        String prompt for OpenAI
    """
    keywords_list = ", ".join([f'"{kw}"' for kw in keywords])
    locations_list = ", ".join([f'"{loc}"' for loc in locations])

    prompt = f"""You are an expert SEO strategist specializing in keyword clustering and intelligent search optimization.

Your task is to generate smart, realistic keyword variations for each provided keyword. You must analyze each keyword to determine if it's a SERVICE or a SPECIALTY/PRODUCT and generate appropriate variations.

KEYWORDS TO PROCESS: {keywords_list}

TARGET TYPE: {target}
AVAILABLE LOCATIONS: {locations_list}

CRITICAL INSTRUCTIONS:

1. ANALYZE EACH KEYWORD TYPE:
   - SERVICE KEYWORDS (agencies, consultants, contractors, repair, cleaning, etc.): Use location-based patterns with actual locations
   - SPECIALTY/PRODUCT KEYWORDS (specific products, techniques, materials, etc.): Focus on descriptive variations without locations

2. FOR SERVICE KEYWORDS - Generate realistic location-based variations using the actual locations provided:
   - "[keyword] near me"
   - "[keyword] in [actual_location]" (use locations from the list)
   - "best [keyword] in [actual_location]"
   - "[actual_location] [keyword]"
   - "top [keyword] [actual_location]"
   - "[keyword] services [actual_location]"

3. FOR SPECIALTY/PRODUCT KEYWORDS - Generate descriptive, realistic variations (NO locations):
   - "best [keyword]"
   - "[keyword] solutions"
   - "professional [keyword]"
   - "[keyword] experts"
   - "custom [keyword]"
   - "[keyword] specialists"
   - "affordable [keyword]"
   - "premium [keyword]"

4. TARGET-SPECIFIC ADAPTATIONS:
   - LOCAL: Use specific cities/areas from the locations list, include "near me"
   - NATIONAL: Use broader regional terms, "top [keyword] in [location]"
   - GLOBAL: For services, still use locations but add "international" terms; for specialties, focus on global descriptors

5. GENERATE EXACTLY {max_cluster} variations per keyword
6. Make variations natural and commonly searched
7. Use the actual location names provided, don't use variables or placeholders
8. For services, mix location-based and "near me" variations
9. For specialties/products, focus on quality and type descriptors

EXAMPLES:

For "digital marketing agency" (SERVICE) with locations ["New York", "Brooklyn"]:
- "digital marketing agency near me"
- "best digital marketing agency in New York"
- "Brooklyn digital marketing agency"
- "digital marketing agency services Brooklyn"

For "responsive web design" (SPECIALTY):
- "professional responsive web design"
- "custom responsive web design solutions"
- "best responsive web design services"
- "responsive web design experts"

OUTPUT FORMAT (JSON):
{{
  "keywords": [
    {{
      "keyword": "original_keyword_1",
      "cluster": [
        "realistic variation 1",
        "realistic variation 2",
        "realistic variation 3"
      ]
    }}
  ]
}}

Analyze each keyword intelligently and generate realistic, search-optimized variations using actual locations for services:"""

    return prompt
