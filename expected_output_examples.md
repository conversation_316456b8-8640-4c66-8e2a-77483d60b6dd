# Expected Output Examples

## Input Example
```json
{
    "keywords": ["digital marketing agency", "responsive web design", "plumbing services"],
    "maxCluster": 4,
    "target": "Local",
    "locations": ["New York", "Brooklyn", "Manhattan"]
}
```

## Expected Output

### Service Keywords (Location-Based)
For **"digital marketing agency"** (SERVICE):
```json
{
    "keyword": "digital marketing agency",
    "cluster": [
        "digital marketing agency near me",
        "best digital marketing agency in New York",
        "Brooklyn digital marketing agency",
        "digital marketing agency services Manhattan"
    ]
}
```

For **"plumbing services"** (SERVICE):
```json
{
    "keyword": "plumbing services", 
    "cluster": [
        "plumbing services near me",
        "emergency plumbing services New York",
        "best plumbing services in Brooklyn",
        "Manhattan plumbing services"
    ]
}
```

### Specialty Keywords (Descriptive)
For **"responsive web design"** (SPECIALTY):
```json
{
    "keyword": "responsive web design",
    "cluster": [
        "professional responsive web design",
        "custom responsive web design solutions", 
        "best responsive web design services",
        "responsive web design experts"
    ]
}
```

## Key Improvements

1. **No Variables**: Direct use of actual location names instead of {CITY}, {STATE}, {COUNTRY} placeholders
2. **Smart Classification**: 
   - Services get location-based variations
   - Specialties get descriptive variations
3. **Realistic Patterns**: 
   - "near me" for local searches
   - "best [service] in [location]" 
   - "[location] [service]"
   - Quality descriptors for specialties

## Different Target Types

### Local Target
- Uses specific cities: "New York", "Brooklyn", "Manhattan"
- Includes "near me" variations
- Focus on immediate area

### National Target  
- Uses broader regions: "California", "Texas", "Florida"
- "top [service] in [state]" patterns
- Regional focus

### Global Target
- Uses countries: "United States", "Canada", "United Kingdom"
- For services: still uses locations but adds international terms
- For specialties: focuses on global descriptors like "international", "worldwide"
