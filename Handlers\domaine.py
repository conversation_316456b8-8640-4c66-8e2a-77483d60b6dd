import json
import random
import azure.functions as func
import openai
from openai import OpenAI
from scipy import spatial

client = OpenAI(
  
  api_key ='********************************************************',
)

def get_embedding(self, text, model="text-embedding-ada-002"):
    global client
    return client.embeddings.create(input=[text], model=model).data[0].embedding
def cosine_similarity(embedding1,embedding2):
    cos_sim = 1 - spatial.distance.cosine(embedding1, embedding2)
    return cos_sim


domaine_responses = [
    "Here is a collection of domain names for you to consider. I think you'll find some of them quite catchy and memorable. If any of them catch your eye, simply click on your favorite, and we'll guide you to the next step.",
"I've prepared a list of domain names that I think you'll find interesting. They have a catchy and memorable quality to them. If any of them pique your interest, go ahead and click on your preferred choice, and we'll move on to the next stage.",
"Check out this lineup of domain names I've compiled for you. They're designed to be catchy and easy to remember. If any of them resonate with you, simply click on the one you like, and we'll continue to the next part of the process.",
"I've put together a list of domain names that I believe have a catchy and memorable appeal. Take a look and see if any of them capture your attention. If you find one you like, click on it, and we'll proceed to the next step together.",
"Take a moment to explore this selection of domain names. They've been carefully chosen to be catchy and easy to remember. If any of them stand out to you, just click on your preferred option, and we'll guide you through the next phase.",
"Behold, a list of domain names that have a catchy and memorable quality. Browse through them, and if you spot one that resonates with you, click on it to proceed to the next step.",
"Discover this array of domain names that are both catchy and easy to remember. I've curated them for your consideration. If any of them appeal to you, simply click on your favorite, and we'll move forward together.",
"Feast your eyes on this assortment of domain names that possess a catchy and memorable nature. Take your time to go through them, and when you find one you like, click on it to progress to the next stage.",
"Take a peek at this collection of domain names that exude a catchy and memorable vibe. If any of them catch your fancy, go ahead and click on your chosen name, and we'll continue on to the next step.",
"Behold, a handpicked selection of domain names designed to be catchy and easy to remember. Scan through the options, and if one catches your attention, simply click on it to proceed to the next phase."
]
selected_domaine_responses = [
"Perfect! Thank you for choosing {selected_domain} as your domain name. I'm glad you're happy with your selection. We're now ready to move on to the next step. Just let me know if there's anything else you need assistance with.",
"Excellent choice! {selected_domain} it is. I'm thrilled that you've found the perfect domain name. Let's proceed to the next step together. If you have any further questions or requests, feel free to ask.",
"Wonderful! You've selected {selected_domain} as your domain name. Great decision! Now, let's move forward to the next step. Should you require any additional support or have any concerns, please don't hesitate to let me know.",
"Fantastic! {selected_domain} has been set as your domain name. I'm excited to help you progress to the next stage. If there's anything else you'd like to discuss or if you need any further assistance, just say the word.",
"Congratulations on choosing {selected_domain}! That's a brilliant domain name. Now, let's take the next step together. If there's anything specific you'd like to address or if you need any further guidance, please feel free to share.",
"Marvelous choice! {selected_domain} is now confirmed as your domain name. I'm thrilled to guide you through the next step. Should you require any additional information or have any special requests, please don't hesitate to ask.",
"Great news! {selected_domain} is locked in as your domain name. It's a pleasure to accompany you to the next step. If there's anything else you'd like to explore or if you have any questions, I'm here to assist you.",
"Awesome! Your domain name, {selected_domain}, is all set. I'm excited to help you proceed to the next stage. If there's anything specific you'd like to discuss or if you need any further support, please let me know.",
"Well done on selecting {selected_domain} as your domain name! I'm delighted to be part of your journey to the next step. If there's anything else you'd like to cover or if you require any further assistance, feel free to reach out.",
"Brilliant choice! {selected_domain} is now officially yours. Let's advance to the next step together. Should you have any additional inquiries or if there's anything else I can do to help, please don't hesitate to inform me."
]
class DomainSuggestionObject:
    def __init__(self, firstname, lastname, business_name, description, wisheddomain, olddomain):
        self.firstname = firstname
        self.lastname = lastname
        self.business_name = business_name
        self.description = description
        self.wisheddomain = wisheddomain
        self.olddomain = olddomain


def validate_required_fields(data):
    required_fields = ['question', 'context', 'iteration']
    missing_fields = [field for field in required_fields if field not in data]

    if missing_fields:
        return func.HttpResponse(json.dumps({'error': f"Missing required field(s): {', '.join(missing_fields)}"}), status_code =400)

    if int(data["iteration"])>6:
        return func.HttpResponse(json.dumps({'error': f"Max iterations exceeded"}), status_code =400)

    return None


def validate_optional_fields(data):
    # optional_fields = ['personName', 'businessName', 'businessDescription', 'wisheddomain', 'olddomain']
    # invalid_fields = [field for field in optional_fields if field in data and not isinstance(data[field], str)]

    # if 'variables' in data and not isinstance(data['variables'], list):
    #     invalid_fields.append('variables')

    # if invalid_fields:
    #     return func.HttpResponse(json.dumps({'error': f"Invalid data for optional field(s): {', '.join(invalid_fields)}"}), 400

    return None


def is_question_valid(question, context):
    # Check if the question is valid and in sync with the context
    # Replace with your validation logic
    return True

def ask_user_more_infos (question,additional_fields):
    # Load additional information about user/company and business description
    # and proposed domain names already
    # Replace with your logic to fetch additional data

    conversation = []
    possible_informations = []
    for key, value in additional_fields.items():
        if value and value != 'NULL':
            conversation.append((key, value))
        else:
            if key in ['businessName', 'businessDescription', 'wisheddomain', 'olddomain'] :
                possible_informations.append(key)


    non_null_keys = [key for key, value in additional_fields.items() if  key in ['businessName', 'businessDescription', 'wisheddomain', 'olddomain'] and value is None or value == "NULL"]


    possible_informations = ""
    if non_null_keys != []:
        possible_informations = ",".join(non_null_keys)
    if(len(conversation) > 0 ) :
        log = "USER: i want you to suggest me a domain name for my business\nBOT: What is your {}?\n".format(conversation[0][0])
        for i in range(len(conversation)):
            if i == 0:
                log += "USER: My {} is {}\n".format(conversation[i][0], conversation[i][1])
            else:
                log += "BOT: What is your {}?\nUSER: My {} is :{}\n".format(conversation[i][0], conversation[i][0], conversation[i][1])
    else:
        log = f"""USER : {question}"""

    # missingfields_with_null_values = {key: value for key, value in missingfields.items() if value == "NULL" or value == "null" or value is None }
    # missingfields_with_null_values["userIntent"] = "based on only user_input choose one of the intents : user_have_different_intent_and_dont_want_tohave_domain|user_not_satisfied_with_suggestion|user_explains_his_business_to_have_accurate_suggestion|user_have_specific_domaine_that_he_wants|user_want_to_stop|user_want_to_contact_admin"
    gpt_prompt = f"""
    for the giving conversation suggest a bot_response (as a json with one key bot_response) to ask user more informations to give him accurate domain suggestion
    ------------
    CONVERSATION:
    BOT : Hi ,i'm Sara , your assistant to create your website is there anything i can help you with
    {log}
    BOT : ( bot_response will goes here)
    ------------
    possible informations : {possible_informations}
    ------------
    NB1 :  if you already have the person name you can use it in your bot_response if not ask user for its name first
    NB2 : return a valid json file that can be loaded
    Output json:
    """


    iter = 0
    while (iter < 3):
        try:

            messages = [{"role":"user","content":gpt_prompt}]
            result = client.chat.completions.create(
                model="gpt-4.1-mini",
                messages=messages
            )
            response_additional_fields = json.loads(result.choices[0].message.content)
            # Update the missing_fields dictionary with the filled values
            additional_fields["bot_response"] = response_additional_fields["bot_response"]
            break
        except:
            iter +=1
            continue



    return additional_fields

def get_additional_data(question, additional_fields):
    # Load additional information about user/company and business description
    # and proposed domain names already
    # Replace with your logic to fetch additional data


    conversation = []
    for key, value in additional_fields.items():
        if value and value != 'NULL':
            conversation.append((key, value))
    if(len(conversation) > 0 ) :
        log = "USER: i want you to suggest me a domain name for my business\nBOT: What is your {}?\n".format(conversation[0][0])
        for i in range(len(conversation)):
            if i == 0:
                log += "USER: My {} is {}\n".format(conversation[i][0], conversation[i][1])
            else:
                if(conversation[i][0] == "variables"):
                    for ii,variable in enumerate(conversation[i][1]):
                        if ii == len(conversation[i][1])-1:
                            log += "BOT: What do you thing about domain name : {}?\nUSER: {}\n".format(variable,question)
                        else:
                            log += "BOT: What do you thing about domain name : {}?\nUSER: Nah i don't like it\n".format(variable)

                else:
                    log += "BOT: What is your {}?\nUSER: My {} is :{}\n".format(conversation[i][0], conversation[i][0], conversation[i][1])

        if(additional_fields["variables"] == []):
            log += "BOT: Tell me more about your business to give you acurrate suggestion?\nUSER: {}\n".format(question)

    else:
        log = f"""USER : {question}"""

    # missingfields_with_null_values = {key: value for key, value in missingfields.items() if value == "NULL" or value == "null" or value is None }
    # missingfields_with_null_values["userIntent"] = "based on only user_input choose one of the intents : user_have_different_intent_and_dont_want_tohave_domain|user_not_satisfied_with_suggestion|user_explains_his_business_to_have_accurate_suggestion|user_have_specific_domaine_that_he_wants|user_want_to_stop|user_want_to_contact_admin"
    gpt_prompt = f"""
    from conversation extract informations from user input about the userand his business that match giving json file and replace their values in the correspandent fields, return back filled json object
    leave "NULL" for empty elements
    ------------
    CONVERSATION:
    BOT : Hi ,i'm Sara , your assistant to create your website is there anything i can help you with
    {log}
    BOT : ( bot_response will goes here)
    ------------
    data json : {additional_fields}
    NB : don't be interested in anything rather than domain intent
    NB : try to fill business_name,business_description, olddomain, wisheddomaine with any releavant information from the conversation i fthere exists else 'NULL'
    NB2 : for bot_response generate a response related with domain name context , either present the suggested domain (one suggested domain with extension .com and put it in suggested_domain value too  ) or ask for more infos
    NB3 : for suggested_domain extract domain suggestion from bot_response if exists
    NB4 : return a valid json file that can be loaded
    NB5 : only one domain suggestion needed , don't suggest more than one domain name
    Output json:
    """

    print("2 . additional_data prompt",gpt_prompt,flush=True)

    iter = 0
    while (iter < 3):
        try:

            messages = [{"role":"user","content":gpt_prompt}]
            result = client.chat.completions.create(
                model="gpt-4.1-mini",
                messages=messages
            )
            response_additional_fields = json.loads(result.choices[0].message.content)

            old_conv = f"""BOT : Hi ,i'm Sara , your assistant to create your website is there anything i can help you with
                            {log}
                        """

            conformity = conformity_check2(old_conv, response_additional_fields )
            print("2.2 . additional_data conformity",gpt_prompt,flush=True)

            if not conformity["is_conform"]:
                return ask_user_more_infos(question,additional_fields)




            # Update the missing_fields dictionary with the filled values
            for key, value in response_additional_fields.items():
                if value and value != 'NULL':
                    additional_fields[key] = value
                else:
                    if( not additional_fields[key] or additional_fields[key] == 'NULL'):
                        additional_fields[key] = None

            additional_fields["suggested_domain"] = conformity["suggested_domain"]
            additional_fields["response2"] = response_additional_fields["response"]
            break
        except:
            iter +=1
            continue

    print("3 . additional_data resullt",additional_fields,flush=True)




    if not has_enough_information(additional_fields):
        additional_fields["variables"] = []
        additional_fields["suggested_domain"] = None
        additional_fields["bot_response"] = None


        return ask_user_more_infos(question,additional_fields)

    return additional_fields





    # return {
    #     'person_name': 'John',
    #     'business_name': 'ABC Company',
    #     'business_description': 'A company that specializes in car repairs',
    #     'wished_domain': '',
    #     'old_domain': 'fixcar.com'
    # }


def generate_domain_suggestion(additional_data):
    variables = []

    suggested_domain = additional_data['suggested_domain']
    wished_domain = additional_data['wisheddomain']
    old_domain = additional_data['olddomain']
    person_name = additional_data['personName']
    business_name = additional_data['businessName']
    business_description = additional_data['businessDescription']
    variables = additional_data['variables']


    wished_domain =  format_domain(wished_domain)
    suggested_domain =  format_domain(suggested_domain)
    old_domain =  format_domain(old_domain)


    if not variables:
        variables = []



    if wished_domain or old_domain or suggested_domain:
            if(suggested_domain and suggested_domain not in variables):
                variables.append( suggested_domain)
                print("2. generate_domain_suggestion suggested_domain",variables,flush=True)

                return variables

            elif(wished_domain and wished_domain not in variables):
                print("2. generate_domain_suggestion wished_domain",wished_domain,variables,flush=True)
                variables.append( wished_domain)
                return variables

            elif(old_domain and old_domain not in variables):
                print("2. generate_domain_suggestion old_domain",old_domain,variables,flush=True)
                variables.append( old_domain)
                return variables

    # else:

    #     if person_name and business_name and business_description:
    #         suggestion = f"Suggestion based on {person_name}, {business_name}, {business_description}"
    #         variables.append(suggestion)

    return None


def has_enough_information(additional_data):

    wished_domain = additional_data['wisheddomain']
    old_domain = additional_data['olddomain']
    person_name = additional_data['personName']
    business_name = additional_data['businessName']
    business_description = additional_data['businessDescription']
    # Check if either wished_domain or old_domain is provided


    if wished_domain or old_domain or business_description or business_name:
        return True

    # Check if at least two of the three parameters are provided
    parameter_count = sum(1 for param in [person_name, business_name, business_description] if param and param != "NULL")
    if parameter_count >= 2:
        return True

    return False

def format_domain(domain):
    if not domain :
        return None
    # Remove spaces
    domain = domain.replace(" ", "")

    # Remove "www" if present
    domain = domain.replace("www.", "")

    # Remove "http://" or "https://" if present
    if domain.startswith("http://"):
        domain = domain[7:]
    elif domain.startswith("https://"):
        domain = domain[8:]

    # Remove the extension
    parts = domain.split(".")
    main_part = ".".join(parts[:-1])



    if (main_part == "" or main_part == None or main_part == "NULL" or main_part == "null" or main_part == "null"):
        return None
    return main_part.lower()

def conformity_check (old_conv, response_additional_fields):
    gpt_prompt = f"""
    check json_data json values (not null ones) , if are conform and seems to be extracted from conversation not some random generated data
    return json object with one bool key is_conform based on  that criteria
    ------------
    CONVERSATION:
    {old_conv}
    ------------
    json_data {response_additional_fields}
    ------------
    NB2 : return a valid json file that can be loaded
    Output json:
    """


    iter = 0

    while (iter < 3):
        try:

            messages = [{"role":"user","content":gpt_prompt}]
            result = client.chat.completions.create(
                model="gpt-4.1-mini",
                messages=messages
            )
            response_additional_fields = json.loads(result.choices[0].message.content)
            # Update the missing_fields dictionary with the filled values



            if( response_additional_fields["is_conform"] == True or response_additional_fields["is_conform"] == False ) :
                return response_additional_fields["is_conform"]
                break
            else:
                iter +=1
                continue
        except:
            iter +=1
            continue
    return False

def conformity_check2(old_conv, response_additional_fields):
    gpt_prompt = f"""
    1 . check json_data json values (not null ones) , if are conform and seems to be extracted from conversation not some random generated data
    2 . extract a suggested domain if exists in the finale bot_response message else put 'NULL'. if there is multiple suggestions choose the last one only
    return json object with one tow keys is_conform, suggested_domain based on  that tow criterias
    ------------
    CONVERSATION:
    {old_conv}
    ------------
    json_data {response_additional_fields}
    ------------
    NB2 : return a valid json file that can be loaded
    Output json:
    """


    iter = 0

    while (iter < 3):
        try:

            messages = [{"role":"user","content":gpt_prompt}]
            result = client.chat.completions.create(
                model="gpt-4.1-mini",
                messages=messages
            )
            response_additional_fields = json.loads(result.choices[0].message.content)
            # Update the missing_fields dictionary with the filled values



            if( response_additional_fields["suggested_domain"] and (response_additional_fields["is_conform"] == True or response_additional_fields["is_conform"] == False) ) :
                return response_additional_fields
                break
            else:
                iter +=1
                continue
        except:
            iter +=1
            continue
    return False





def handle_get_domain_variables(data):

    error_response = validate_optional_fields(data)
    if error_response:
        return error_response

    question = data['question']
    context = data['context']
    iteration = data['iteration']
    variables = data.get('variables', [])
    person_name = data.get('personName', 'NULL')
    business_name = data.get('businessName', 'NULL')
    business_description = data.get('businessDescription', 'NULL')
    wished_domain = data.get('wisheddomain', 'NULL')
    old_domain = data.get('olddomain', 'NULL')

    if variables == None:
        variables = []
    if not is_question_valid(question, context):
        return func.HttpResponse(json.dumps({'error': 'Invalid question for GetDomainVariables context'}), status_code = 400)


    additional_fields = {
        'personName': person_name,
        'businessName': business_name,
        'businessDescription': business_description,
        'wisheddomain': wished_domain,
        'olddomain': old_domain,
        'variables': variables,
        'suggested_domain': 'NULL',
        'bot_response': 'NULL',
    }


    additional_data = get_additional_data(question, additional_fields)
    print("1 . additional_data ",additional_data,flush=True)

    # if not has_enough_information(additional_data):
    #     # ask_user_more_infos ()
    #     return func.HttpResponse(json.dumps({'error': 'Not enough information for domain suggestion'}), 400)

    # domain_suggestion = DomainSuggestionObject(
    #     firstname=additional_data['person_name'],
    #     lastname='',
    #     business_name=additional_data['business_name'],
    #     description=additional_data['business_description'],
    #     wisheddomain=additional_data['wished_domain'],
    #     olddomain=additional_data['old_domain']
    # )

    # if generate_domain_suggestion(additional_data):
    #     variables += generate_domain_suggestion(additional_data)
    response_variables = generate_domain_suggestion(additional_data)
    if response_variables:
        variables = response_variables
        additional_data['bot_response'] = random.choice(domaine_responses)

    # Continue with further processing using the domain_suggestion object and additional_data
    # ...

    response = {
        'variables': variables,
        'personName': additional_data['personName'],
        'businessName': additional_data['businessName'],
        'businessDescription': additional_data['businessDescription'],
        'response': additional_data['bot_response'],
        'question': question,
        'context': context,
        'iteration': iteration+1
    }
    # response = {
    #     'variables': variables,
    #     'personName': person_name,
    #     'businessName': business_name,
    #     'businessDescription': business_description,
    #     'wisheddomain': wished_domain,
    #     'olddomain': old_domain,
    #     'question': question,
    #     'context': context,
    #     'iteration': iteration
    # }

    return func.HttpResponse(json.dumps(response), mimetype="application/json", status_code=200)


def handle_save_domain_variables(data):

    error_response = validate_optional_fields(data)
    if error_response:
        return error_response

    question = data['question']
    context = data['context']
    iteration = data['iteration']
    variables = data.get('variables', [])
    person_name = data.get('personName', 'NULL')
    business_name = data.get('businessName', 'NULL')
    business_description = data.get('businessDescription', 'NULL')
    wished_domain = data.get('wisheddomain', 'NULL')
    old_domain = data.get('olddomain', 'NULL')

    if variables == None:
        variables = []

    if len(variables) == 0:
        func.HttpResponse(json.dumps({'error': f"No selected domain found"}), status_code = 400)


    selected_domain = variables[-1]
    response = random.choice(selected_domaine_responses).replace("{selected_domain}",selected_domain)



    additional_fields = {
        'personName': person_name,
        'businessName': business_name,
        'businessDescription': business_description,
        'variables': variables,
        'response': response,
        'iteration': iteration+1,
        'context':context,
        'question':question
    }



    return func.HttpResponse(json.dumps(additional_fields), mimetype="application/json", status_code=200)
