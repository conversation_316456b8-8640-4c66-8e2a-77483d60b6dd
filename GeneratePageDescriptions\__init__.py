# Example payload to call this endpoint:
# {
#   "keywords": [
#     {
#       "keyword": "digital marketing agency",
#       "cluster": [
#         "digital marketing agency near me",
#         "best digital marketing agency in New York",
#         "Brooklyn digital marketing agency",
#         "digital marketing agency services Brooklyn"
#       ]
#     },
#     {
#       "keyword": "web design",
#       "cluster": [
#         "best web design",
#         "professional web design solutions",
#         "custom web design services",
#         "affordable web design"
#       ]
#     }
#   ]
# }
#
# Expected output format:
# {
#   "pages": [
#     {
#       "description": "Professional digital marketing services including social media management, PPC advertising, and SEO optimization. Expert team delivering measurable results for businesses in New York and Brooklyn.",
#       "pageType": "Service",
#       "pageTitle": "Digital Marketing Agency - Professional Marketing Services"
#     },
#     {
#       "description": "Professional web design services creating responsive, modern websites that drive results. Custom solutions for businesses of all sizes with focus on user experience and conversion optimization.",
#       "pageType": "Service",
#       "pageTitle": "Professional Web Design Services - Custom Website Development"
#     }
#   ]
# }

import azure.functions as func
import json
import logging
from openai import OpenAI

# Initialize the OpenAI client
client = OpenAI(
    api_key='********************************************************',
)

def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Python HTTP trigger function processed a request to generate page descriptions.')

    try:
        # Parse JSON body
        try:
            req_body = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps({'error': 'Invalid JSON body.'}),
                mimetype="application/json",
                status_code=400
            )

        # Extract required fields
        keywords = req_body.get('keywords')

        # Validate required fields
        if keywords is None:
            return func.HttpResponse(
                json.dumps({'error': 'Missing required field: keywords'}),
                mimetype="application/json",
                status_code=400
            )

        # Validate field types and values
        if not isinstance(keywords, list) or len(keywords) == 0:
            return func.HttpResponse(
                json.dumps({'error': 'keywords must be a non-empty list'}),
                mimetype="application/json",
                status_code=400
            )

        # Validate keyword structure
        for i, keyword_data in enumerate(keywords):
            if not isinstance(keyword_data, dict):
                return func.HttpResponse(
                    json.dumps({'error': f'Each keyword must be an object. Invalid keyword at index {i}'}),
                    mimetype="application/json",
                    status_code=400
                )

            if 'keyword' not in keyword_data or 'cluster' not in keyword_data:
                return func.HttpResponse(
                    json.dumps({'error': f'Each keyword must have "keyword" and "cluster" fields. Invalid keyword at index {i}'}),
                    mimetype="application/json",
                    status_code=400
                )

            if not isinstance(keyword_data['keyword'], str) or not keyword_data['keyword'].strip():
                return func.HttpResponse(
                    json.dumps({'error': f'Keyword must be a non-empty string. Invalid keyword at index {i}'}),
                    mimetype="application/json",
                    status_code=400
                )

            if not isinstance(keyword_data['cluster'], list) or len(keyword_data['cluster']) == 0:
                return func.HttpResponse(
                    json.dumps({'error': f'Keyword cluster must be a non-empty list. Invalid keyword at index {i}'}),
                    mimetype="application/json",
                    status_code=400
                )

        # Generate page descriptions
        pages_response = generate_page_descriptions(keywords)

        if pages_response:
            return func.HttpResponse(
                json.dumps(pages_response),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({'error': 'Failed to generate page descriptions.'}),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logging.error(f"Exception occurred: {e}")
        return func.HttpResponse(
            json.dumps({'error': str(e)}),
            mimetype="application/json",
            status_code=500
        )

def generate_page_descriptions(keywords):
    """
    Generate page descriptions using OpenAI GPT-4o-mini model.

    Args:
        keywords: List of keyword objects with keyword and cluster fields

    Returns:
        Dictionary with page descriptions or None if failed
    """
    try:
        # Create the prompt for OpenAI
        prompt = create_page_descriptions_prompt(keywords)
        
        # Call OpenAI API
        messages = [{"role": "user", "content": prompt}]
        result = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=messages,
            response_format={"type": "json_object"},
            temperature=0.7  # Slight creativity for descriptions
        )
        
        # Parse the response
        response_json = json.loads(result.choices[0].message.content)
        
        # Validate and format the response
        if "pages" not in response_json:
            logging.error("OpenAI response missing 'pages' field")
            return None
            
        # Validate page structure
        formatted_pages = []
        for page_data in response_json["pages"]:
            if all(field in page_data for field in ["description", "pageType", "pageTitle"]):
                formatted_pages.append({
                    "description": page_data["description"],
                    "pageType": page_data["pageType"],
                    "pageTitle": page_data["pageTitle"]
                })
        
        return {"pages": formatted_pages}
        
    except Exception as e:
        logging.error(f"Error generating page descriptions: {e}")
        return None

def create_page_descriptions_prompt(keywords):
    """
    Create the prompt for OpenAI to generate page descriptions.

    Args:
        keywords: List of keyword objects with keyword and cluster fields

    Returns:
        String prompt for OpenAI
    """

    # Format keywords for the prompt
    keywords_text = ""
    for i, keyword_data in enumerate(keywords, 1):
        cluster_keywords = ", ".join(keyword_data['cluster'])
        keywords_text += f"{i}. **{keyword_data['keyword']}**\n   Cluster: {cluster_keywords}\n\n"

    prompt = f"""You are an expert web strategist and content architect specializing in creating comprehensive page strategies for businesses.

Your task is to analyze keyword clusters and generate strategic page descriptions for NEW PAGES that should be created. These pages should complement existing website content and target specific keyword opportunities.

KEYWORD CLUSTERS TO ANALYZE:
{keywords_text}

CRITICAL INSTRUCTIONS:

1. **ANALYZE EACH CLUSTER**: Determine what type of page would best serve the keyword intent and business goals

2. **PAGE TYPES**: Choose the most appropriate page type for each cluster:
   - "Service" - For service-based keywords (repairs, consulting, etc.)
   - "Product" - For product-focused keywords
   - "Landing" - For targeted marketing campaigns
   - "Category" - For broad topic categories
   - "Location" - For location-specific content
   - "Blog" - For informational/educational content

3. **GENERATE STRATEGIC PAGES**: For each cluster, create pages that:
   - Target the primary keyword intent
   - Serve business conversion goals
   - Provide comprehensive coverage of the topic
   - Are optimized for search engines
   - Avoid duplicating existing content

4. **PAGE DESCRIPTIONS**: Write compelling, detailed descriptions (2-3 sentences) that:
   - Explain the page's purpose and value proposition
   - Include key benefits and features
   - Target the primary audience
   - Mention relevant services/products

5. **PAGE TITLES**: Create SEO-optimized titles that:
   - Include primary keywords naturally
   - Are compelling and click-worthy
   - Follow best practices (50-60 characters)
   - Clearly communicate the page value

6. **STRATEGIC CONSIDERATIONS**:
   - Consider user search intent (informational, commercial, transactional)
   - Think about the customer journey stage
   - Ensure pages complement each other
   - Focus on conversion opportunities

EXAMPLES:

For cluster "iPhone Repair" with keywords ["iPhone repair near me", "iPhone screen replacement"]:
- Page Type: "Service"
- Title: "iPhone Repair Services - Professional Mobile Device Repair"
- Description: "Expert iPhone repair services including screen replacement, battery replacement, and water damage repair. Same-day service available with certified technicians and warranty coverage."

For cluster "Web Design" with keywords ["professional web design", "custom web design"]:
- Page Type: "Service"
- Title: "Professional Web Design Services - Custom Website Development"
- Description: "Professional web design services creating responsive, modern websites that drive results. Custom solutions for businesses of all sizes with ongoing support and optimization."

OUTPUT FORMAT (JSON):
{{
  "pages": [
    {{
      "description": "Detailed page description explaining value and benefits",
      "pageType": "Service|Product|Landing|Category|Location|Blog",
      "pageTitle": "SEO-optimized page title with primary keywords"
    }}
  ]
}}

Analyze the keyword clusters and generate strategic page recommendations:"""

    return prompt
