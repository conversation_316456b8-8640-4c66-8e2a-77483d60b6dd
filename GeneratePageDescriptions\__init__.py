# Example payload to call this endpoint:
# {
#   "clusters": [
#     {
#       "name": "Fix iPhone",
#       "clusters": ["iPhone repair near me", "best iPhone repair in New York", "iPhone screen replacement", "iPhone battery replacement"]
#     },
#     {
#       "name": "Web Design",
#       "clusters": ["professional web design", "custom web design solutions", "responsive web design services", "web design experts"]
#     }
#   ]
# }
#
# Expected output format:
# {
#   "pages": [
#     {
#       "description": "Comprehensive iPhone repair services including screen replacement, battery replacement, and water damage repair. Expert technicians with same-day service available.",
#       "pageType": "Service",
#       "pageTitle": "iPhone Repair Services - Professional Mobile Device Repair"
#     },
#     {
#       "description": "Professional web design services creating responsive, modern websites that drive results. Custom solutions for businesses of all sizes.",
#       "pageType": "Service", 
#       "pageTitle": "Professional Web Design Services - Custom Website Development"
#     }
#   ]
# }

import azure.functions as func
import json
import logging
from openai import OpenAI

# Initialize the OpenAI client
client = OpenAI(
    api_key='********************************************************',
)

def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Python HTTP trigger function processed a request to generate page descriptions.')

    try:
        # Parse JSON body
        try:
            req_body = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps({'error': 'Invalid JSON body.'}),
                mimetype="application/json",
                status_code=400
            )

        # Extract required fields
        clusters = req_body.get('clusters')

        # Validate required fields
        if clusters is None:
            return func.HttpResponse(
                json.dumps({'error': 'Missing required field: clusters'}),
                mimetype="application/json",
                status_code=400
            )

        # Validate field types and values
        if not isinstance(clusters, list) or len(clusters) == 0:
            return func.HttpResponse(
                json.dumps({'error': 'clusters must be a non-empty list'}),
                mimetype="application/json",
                status_code=400
            )

        # Validate cluster structure
        for i, cluster in enumerate(clusters):
            if not isinstance(cluster, dict):
                return func.HttpResponse(
                    json.dumps({'error': f'Each cluster must be an object. Invalid cluster at index {i}'}),
                    mimetype="application/json",
                    status_code=400
                )
            
            if 'name' not in cluster or 'clusters' not in cluster:
                return func.HttpResponse(
                    json.dumps({'error': f'Each cluster must have "name" and "clusters" fields. Invalid cluster at index {i}'}),
                    mimetype="application/json",
                    status_code=400
                )
            
            if not isinstance(cluster['name'], str) or not cluster['name'].strip():
                return func.HttpResponse(
                    json.dumps({'error': f'Cluster name must be a non-empty string. Invalid cluster at index {i}'}),
                    mimetype="application/json",
                    status_code=400
                )
            
            if not isinstance(cluster['clusters'], list) or len(cluster['clusters']) == 0:
                return func.HttpResponse(
                    json.dumps({'error': f'Cluster clusters must be a non-empty list. Invalid cluster at index {i}'}),
                    mimetype="application/json",
                    status_code=400
                )

        # Generate page descriptions
        pages_response = generate_page_descriptions(clusters)

        if pages_response:
            return func.HttpResponse(
                json.dumps(pages_response),
                mimetype="application/json",
                status_code=200
            )
        else:
            return func.HttpResponse(
                json.dumps({'error': 'Failed to generate page descriptions.'}),
                mimetype="application/json",
                status_code=500
            )

    except Exception as e:
        logging.error(f"Exception occurred: {e}")
        return func.HttpResponse(
            json.dumps({'error': str(e)}),
            mimetype="application/json",
            status_code=500
        )

def generate_page_descriptions(clusters):
    """
    Generate page descriptions using OpenAI GPT-4o-mini model.
    
    Args:
        clusters: List of keyword cluster objects with name and clusters fields
    
    Returns:
        Dictionary with page descriptions or None if failed
    """
    try:
        # Create the prompt for OpenAI
        prompt = create_page_descriptions_prompt(clusters)
        
        # Call OpenAI API
        messages = [{"role": "user", "content": prompt}]
        result = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=messages,
            response_format={"type": "json_object"},
            temperature=0.7  # Slight creativity for descriptions
        )
        
        # Parse the response
        response_json = json.loads(result.choices[0].message.content)
        
        # Validate and format the response
        if "pages" not in response_json:
            logging.error("OpenAI response missing 'pages' field")
            return None
            
        # Validate page structure
        formatted_pages = []
        for page_data in response_json["pages"]:
            if all(field in page_data for field in ["description", "pageType", "pageTitle"]):
                formatted_pages.append({
                    "description": page_data["description"],
                    "pageType": page_data["pageType"],
                    "pageTitle": page_data["pageTitle"]
                })
        
        return {"pages": formatted_pages}
        
    except Exception as e:
        logging.error(f"Error generating page descriptions: {e}")
        return None

def create_page_descriptions_prompt(clusters):
    """
    Create the prompt for OpenAI to generate page descriptions.

    Args:
        clusters: List of keyword cluster objects

    Returns:
        String prompt for OpenAI
    """

    # Format clusters for the prompt
    clusters_text = ""
    for i, cluster in enumerate(clusters, 1):
        cluster_keywords = ", ".join(cluster['clusters'])
        clusters_text += f"{i}. **{cluster['name']}**\n   Keywords: {cluster_keywords}\n\n"

    prompt = f"""You are an expert web strategist and content architect specializing in creating comprehensive page strategies for businesses.

Your task is to analyze keyword clusters and generate strategic page descriptions for NEW PAGES that should be created. These pages should complement existing website content and target specific keyword opportunities.

KEYWORD CLUSTERS TO ANALYZE:
{clusters_text}

CRITICAL INSTRUCTIONS:

1. **ANALYZE EACH CLUSTER**: Determine what type of page would best serve the keyword intent and business goals

2. **PAGE TYPES**: Choose the most appropriate page type for each cluster:
   - "Service" - For service-based keywords (repairs, consulting, etc.)
   - "Product" - For product-focused keywords
   - "Landing" - For targeted marketing campaigns
   - "Category" - For broad topic categories
   - "Location" - For location-specific content
   - "Blog" - For informational/educational content

3. **GENERATE STRATEGIC PAGES**: For each cluster, create pages that:
   - Target the primary keyword intent
   - Serve business conversion goals
   - Provide comprehensive coverage of the topic
   - Are optimized for search engines
   - Avoid duplicating existing content

4. **PAGE DESCRIPTIONS**: Write compelling, detailed descriptions (2-3 sentences) that:
   - Explain the page's purpose and value proposition
   - Include key benefits and features
   - Target the primary audience
   - Mention relevant services/products

5. **PAGE TITLES**: Create SEO-optimized titles that:
   - Include primary keywords naturally
   - Are compelling and click-worthy
   - Follow best practices (50-60 characters)
   - Clearly communicate the page value

6. **STRATEGIC CONSIDERATIONS**:
   - Consider user search intent (informational, commercial, transactional)
   - Think about the customer journey stage
   - Ensure pages complement each other
   - Focus on conversion opportunities

EXAMPLES:

For cluster "iPhone Repair" with keywords ["iPhone repair near me", "iPhone screen replacement"]:
- Page Type: "Service"
- Title: "iPhone Repair Services - Professional Mobile Device Repair"
- Description: "Expert iPhone repair services including screen replacement, battery replacement, and water damage repair. Same-day service available with certified technicians and warranty coverage."

For cluster "Web Design" with keywords ["professional web design", "custom web design"]:
- Page Type: "Service"
- Title: "Professional Web Design Services - Custom Website Development"
- Description: "Professional web design services creating responsive, modern websites that drive results. Custom solutions for businesses of all sizes with ongoing support and optimization."

OUTPUT FORMAT (JSON):
{{
  "pages": [
    {{
      "description": "Detailed page description explaining value and benefits",
      "pageType": "Service|Product|Landing|Category|Location|Blog",
      "pageTitle": "SEO-optimized page title with primary keywords"
    }}
  ]
}}

Analyze the keyword clusters and generate strategic page recommendations:"""

    return prompt
