import openai
import azure.functions as func
import json

from Handlers.domaine import handle_get_domain_variables, handle_save_domain_variables
from Handlers.domaine2 import handle_get_domain_variables2, handle_save_domain_variables2
from Handlers.category import handle_save_category, handle_get_category
from Handlers.modules import handle_save_modules, handle_get_modules




def validate_required_fields(data):
    required_fields = ['question', 'context', 'iteration']
    missing_fields = [field for field in required_fields if field not in data]

    if missing_fields:
        return func.HttpResponse(json.dumps({'error': f"Missing required field(s): {', '.join(missing_fields)}"}), mimetype="application/json", status_code=400)

    if int(data["iteration"]) > 6:
        return func.HttpResponse(json.dumps({'error': f"Max iterations exceeded"}), mimetype="application/json", status_code=400)

    return None

def main(req: func.HttpRequest) -> func.HttpResponse:
    try:
        data = req.get_json()

        error_response = validate_required_fields(data)
        if error_response:
            return error_response

        if data["context"] == "GetDomainVariables":
            # Call the corresponding handler function
            result = handle_get_domain_variables2(data)
        elif data["context"] == "GetDomainVariables2":
            # Call the corresponding handler function
            result = handle_get_domain_variables(data)
        elif data["context"] == "SaveDomainVariables":
            # Call the corresponding handler function
            result = handle_save_domain_variables2(data)
        elif data["context"] == "SaveDomainVariables2":
            # Call the corresponding handler function
            result = handle_save_domain_variables(data)
        elif data["context"] == "GetCategory":
            # Call the corresponding handler function
            result = handle_get_category(data)
        elif data["context"] == "SaveCategory":
            # Call the corresponding handler function
            result = handle_save_category(data)
        elif data["context"] == "GetModules":
            # Call the corresponding handler function
            result = handle_get_modules(data)
        elif data["context"] == "SaveModules":
            # Call the corresponding handler function
            result = handle_save_modules(data)
        else:
            return func.HttpResponse(json.dumps({'error': f"Invalid Context"}), mimetype="application/json", status_code=400)

        return result
    except Exception as e:
        return func.HttpResponse(json.dumps({'error': str(e)}), mimetype="application/json", status_code=500)

# Define the Azure Functions entry point
