import logging
import json
import azure.functions as func
from openai import AzureOpenAI
from bs4 import BeautifulSoup, NavigableString
import asyncio
from typing import List, Dict, Optional
from dataclasses import dataclass
from contextlib import asynccontextmanager
import math

@dataclass
class APIConfig:
    api_key: str
    azure_endpoint: str = "https://slicky-open-ai.openai.azure.com/openai/deployments/gpt-4o-mini/chat/completions?api-version=2024-08-01-preview"

# Configure multiple API clients
API_CONFIGS = [
    APIConfig(api_key='********************************************************************************************************************************************************************'),
    APIConfig(api_key='********************************************************************************************************************************************************************'),
    APIConfig(api_key='********************************************************************************************************************************************************************')
]
API_CONFIGS = [
    APIConfig(api_key='********************************************************************************************************************************************************************'),
    APIConfig(api_key='***********************************************************************************************'),
    APIConfig(api_key='********************************************************************************************************************************************************************')
]

API_CONFIGS = [
    APIConfig(api_key='a540bf2106534474a36320ea50d3e17e'),
    APIConfig(api_key='3fZwEDVVbyBmdz2OXpB1oJGt6NUYu6UPLSadtOdFMjg7etyq0nmeJQQJ99BAACHYHv6XJ3w3AAAAACOGqv3g',azure_endpoint="https://ayoub-m6fejja5-eastus2.cognitiveservices.azure.com/openai/deployments/gpt-4o-mini2/chat/completions?api-version=2024-08-01-preview"),


]
# API_CONFIGS = [
#     APIConfig(api_key='********************************************************************************************************************************************************************'),
#     APIConfig(api_key='***********************************************************************************************')
# ]

class ContentSplitter:
    @staticmethod
    def split_sections(sections: List[Dict], num_splits: int) -> List[List[Dict]]:
        """Split sections into approximately equal chunks."""
        if not sections:
            return []
            
        # Calculate sections per split (round up to ensure all sections are covered)
        sections_per_split = math.ceil(len(sections) / num_splits)
        
        # Split sections into chunks
        return [
            sections[i:i + sections_per_split]
            for i in range(0, len(sections), sections_per_split)
        ]

class HTMLContentParser:
    def __init__(self, sections: List[Dict]):
        self.sections = sections
        self.content_list = []
        self.placeholder_count = 0
        self.valid_placeholders = [
            '[header]', 
            '[subHeader]', 
            '[content]', 
            '[mediumcontent]', 
            '[shortContent]', 
            '[longContent]',
            '[icon]',
            '[button]',
            '[ai_img]',
            '[achievementNumber]',
            '[achievementDesc]',
            '[reviewTitle]',
            '[reviewBodyShort]',
            '[reviewBodyMedium]',
            '[reviewBodyLong]',
            '[reviewAuthor]',
            '[subHeaderChild]',
            '[mediumContentChild]'
        ]
        
    def _is_valid_placeholder(self, text: str) -> bool:
        """Check if text contains any of the valid placeholders."""
        return any(placeholder.lower() in text.lower() for placeholder in self.valid_placeholders)
        
    def _process_text_element(self, element: NavigableString, section_suggestion: str) -> Optional[Dict]:
        if not isinstance(element, NavigableString) or not element.strip():
            return None
            
        text = element.strip()
        if not self._is_valid_placeholder(text):
            return None
            
        self.placeholder_count += 1
        return {
            "type": "text",
            "identifier": f"placeholder_{self.placeholder_count}",
            "text": text,
            "suggestion": section_suggestion
        }
        
    def _process_img_placeholder(self, element, section_suggestion: str) -> Optional[Dict]:
        """Process elements with keyword attribute containing [img]."""
        if not element.has_attr('keyword'):
            return None
            
        keyword_value = element['keyword']
        if '[img]' not in keyword_value:
            return None
            
        self.placeholder_count += 1
        placeholder = f"placeholder_{self.placeholder_count}"
        
        content = {
            "type": "img",
            "identifier": placeholder,
            "text": "[img]",
            "suggestion": section_suggestion
        }
        
        element['keyword'] = f"--{placeholder}--"
        return content

    def _process_icon_placeholder(self, element, section_suggestion: str) -> Optional[Dict]:
        """Process elements with icon attribute."""
        if not element.has_attr('icon'):
            return None
            
        icon_value = element['icon']
        if '[icon]' not in icon_value:
            return None
            
        self.placeholder_count += 1
        placeholder = f"placeholder_{self.placeholder_count}"
        
        content = {
            "type": "icon",
            "identifier": placeholder,
            "text": "[icon]",
            "suggestion": section_suggestion
        }
        
        element['icon'] = f"--{placeholder}--"
        return content

    def _process_button_placeholder(self, element, section_suggestion: str) -> Optional[Dict]:
        """Process elements with buttonsuggestion attribute."""
        if not element.has_attr('buttonsuggestion'):
            return None
            
        suggestion_value = element['buttonsuggestion']
        if '[button]' not in suggestion_value:
            return None
            
        self.placeholder_count += 1
        placeholder = f"placeholder_{self.placeholder_count}"
        
        # Get the original button text for context
        original_text = element.string.strip() if element.string else ""
        
        content = {
            "type": "button",
            "identifier": placeholder,
            "text": "[button]",
            "suggestion": section_suggestion,
            "originalText": original_text  # Include original text for context
        }
        
        element['buttonsuggestion'] = f"--{placeholder}--"
        return content

    def _process_ai_img_placeholder(self, element, section_suggestion: str) -> Optional[Dict]:
        """Process elements with aikeyword attribute."""
        if not element.has_attr('aikeyword'):
            return None
            
        keyword_value = element['aikeyword']
        if '[ai_img]' not in keyword_value:
            return None
            
        self.placeholder_count += 1
        placeholder = f"placeholder_{self.placeholder_count}"
        
        content = {
            "type": "ai_img",
            "identifier": placeholder,
            "text": "[ai_img]",
            "suggestion": section_suggestion
        }
        
        element['aikeyword'] = f"--{placeholder}--"
        return content

    def extract_content_with_placeholders(self) -> str:
        for section in self.sections:
            soup = BeautifulSoup(str(section["sectionContent"]), 'html.parser')
            section_suggestion = section.get("suggestion", "")
            
            # Process text elements
            for element in soup.find_all(string=True, recursive=True):
                if element.parent.name != 'script':
                    content = self._process_text_element(element, section_suggestion)
                    if content:
                        element.replace_with(f"--{content['identifier']}--")
                        self.content_list.append(content)

            # Process elements with [img] keyword attribute
            for element in soup.find_all(attrs={'keyword': '[img]'}):
                content = self._process_img_placeholder(element, section_suggestion)
                if content:
                    self.content_list.append(content)

            # Process elements with [icon] attribute
            for element in soup.find_all(attrs={'icon': '[icon]'}):
                content = self._process_icon_placeholder(element, section_suggestion)
                if content:
                    self.content_list.append(content)

            # Process elements with buttonsuggestion attribute
            for element in soup.find_all(attrs={'buttonsuggestion': '[button]'}):
                content = self._process_button_placeholder(element, section_suggestion)
                if content:
                    self.content_list.append(content)

            # Process elements with aikeyword attribute
            for element in soup.find_all(attrs={'aikeyword': '[ai_img]'}):
                content = self._process_ai_img_placeholder(element, section_suggestion)
                if content:
                    self.content_list.append(content)

            section["sectionContent"] = str(soup)
            
        return json.dumps(self.content_list, indent=4)

    def generate_html_with_replacements(self, json_content: str) -> List[Dict]:
        content_replacements = json.loads(json_content)
        updated_sections = []

        for section in self.sections:
            soup = BeautifulSoup(str(section["sectionContent"]), 'html.parser')
            
            for content in content_replacements:
                placeholder = f"--{content['identifier']}--"
                
                if content['type'] in ['text', 'icon', 'button', 'ai_img']:
                    section["sectionContent"] = section["sectionContent"].replace(
                        placeholder, content.get('text', '')
                    )
                elif content['type'] == 'img':
                    section["sectionContent"] = section["sectionContent"].replace(
                        placeholder, content.get('text', '')
                    )
                                
            updated_sections.append(section)

        return updated_sections



class OpenAIClient:
    def __init__(self, configs: List[APIConfig]):
        self.clients = [
            AzureOpenAI(
                api_key = config.api_key,  
                api_version = "2024-08-01-preview",
                azure_endpoint = config.azure_endpoint
            )
            for config in configs
        ]
        self.num_clients = len(self.clients)
        self.max_retries = 3  # Maximum number of retries per section

    async def process_section_chunk(
        self,
        sections: List[Dict],
        company_info: Dict,
        client_index: int
    ) -> Optional[List[Dict]]:
        """Process a chunk of sections using a specific client with retry logic."""
        client = self.clients[client_index]
        parser = HTMLContentParser(sections)
        json_content = parser.extract_content_with_placeholders()
        prompt = get_prompt(company_info, json_content)
        

            
        retry_count = 0
        while retry_count < self.max_retries:
            try:

                if json_content != []:
                    response = client.chat.completions.create(
                        model="gpt-4o-mini",
                        messages=[{"role": "user", "content": prompt}],
                        response_format={"type": "json_object"}
                    )
                    content = response.choices[0].message.content.strip()
                    result = json.loads(content)
                    
                    # Check for placeholders in content and result
                    # has_placeholder = any([
                    #     "--placeholder" in content,
                    #     "--imagekeyword--" in content,
                    #     "PLACEHOLDER" in content.upper()  # Check for remaining bracket placeholders
                    # ])
                    
                    # if not has_placeholder:
                    # Successfully processed without placeholders
                    processed_sections = parser.generate_html_with_replacements(json.dumps(result["sections"]))
                    
                    # Double-check the final output for any remaining placeholders
                    final_content = json.dumps(processed_sections)
                    if "--PLACEHOLDER" in final_content.upper():
                        logging.warning(f"Retry {retry_count + 1}: Found placeholders in final output")
                        retry_count += 1
                        continue
                else: 
                    processed_sections = parser.generate_html_with_replacements(json.dumps([]))

                return processed_sections
                
                logging.warning(f"Retry {retry_count + 1}: Found placeholders in OpenAI response")
                retry_count += 1
                
            except Exception as e:
                logging.error(f"OpenAI request failed for chunk {client_index} (attempt {retry_count + 1}): {str(e)}")
                retry_count += 1
        
        logging.error(f"Failed to process chunk {client_index} after {self.max_retries} attempts")
        return None

    async def process_all_sections(
        self,
        sections: List[Dict],
        company_info: Dict
    ) -> Optional[List[Dict]]:
        """Process all sections by splitting them across available clients."""
        section_chunks = ContentSplitter.split_sections(sections, self.num_clients)
        
        tasks = [
            self.process_section_chunk(chunk, company_info, i)
            for i, chunk in enumerate(section_chunks)
        ]
        
        results = await asyncio.gather(*tasks)
        
        combined_sections = []
        for result in results:
            if result:
                combined_sections.extend(result)
                
        # Final check for the combined results
        # if combined_sections:
        #     final_content = json.dumps(combined_sections)
        #     if any(marker in final_content.upper() for marker in ["PLACEHOLDER"]):
        #         logging.error("Found placeholders in final combined output")
        #         return None
                
        return combined_sections if combined_sections else None

async def update_sections_with_openai(
    sections: List[Dict],
    company_info: Dict,
    openai_client: OpenAIClient
) -> Optional[List[Dict]]:
    section_indices = company_info.get('sectionsToUpdate', [])
    sections_to_process = [sections[i] for i in section_indices] if section_indices else sections
    
    return await openai_client.process_all_sections(sections_to_process, company_info)

def get_prompt(company_info: Dict, json_content: str) -> str:
    if 'page_description' in company_info:
        return f"""Given the page purpose: {company_info['page_description']}

Let's approach this step by step:
1. First, analyze each section's current placeholder type and its suggestion
2. Then, determine how to adapt the content based on section's purpose
3. Finally, generate content that fits the placeholders while following guidelines

Current sections: {{"sections":{json_content}}}
Company context: {json.dumps(company_info)}

Guidelines for placeholders:
- [header]: Title of the section (max 8 words)
- [subHeader]: Section header/subtitle (max 12 words)
- [content]: Single paragraph content (max 70 words)
- [mediumcontent]: Brief paragraph (max 20 words)
- [shortContent]: Very brief text (max 5 words)
- [longContent]: Multiple paragraphs, use [BREAK] for separation
- [img]: Image keyword (a keyword for searching an image on Unsplash, one word only if the keywords would not be releavent consider use like the industry name to be sure we will have accurate image at least with same theme)
- [icon]: Name of a related icon from svgapi icon library (e.g., 'cart', 'user', 'phone')
- [button]: Improved button text suggestion based on original text and context (max 4 words)
- [ai_img]: Detailed prompt for AI image generation (20-30 words, describing style, mood, and content)
- [achievementNumber]: Quantifiable achievement or metric (e.g., "100% of guests leave more relaxed than when they arrived", "9 out of 10 hot stones say they're living their best life", "200+ sighs of bliss recorded daily", "95% cucumber slice retention rate"). Should be attention-grabbing and express success, excellence, or satisfaction.
- [achievementDesc]: Description of what the achievement reflects, typically in a lighthearted or promotional way (e.g., "Customers are floating in pure bliss", "Guests forget their stress, their worries — and sometimes their name", "Facial glows detected from three rooms away", "Every massage unlocks a little more inner peace (and sometimes old memories)"). Summarizes the result or emotional impact for the customer.
- [reviewTitle]: A short, catchy summary of the review. Examples: “I Transcended. In a Robe.”, “This Spa Unclenched My Soul”, “10/10 Would Melt Again”, “Officially Softer Than a Cloud”
- [reviewBodyShort]: A very short, punchy review (max 150 characters). Examples: “I came in stressed, left a puddle of joy.”, “The steam room gave me clarity and frizz-free hair.”, “I forgot what year it was, in the best way.”
- [reviewBodyMedium]: A balanced, expressive review with light humor or description (max 300 characters). Examples: “This spa didn’t just relax me — it turned me into a sleepy little blanket burrito. My muscles gave up and said thank you.”, “Was it the tea? The massage? The robe? I don’t know — but I left with zero tension and possibly new skin.”
- [reviewBodyLong]: A full-length, narrative-style review (max 800 characters). Examples: “From the moment I walked in, I knew I wasn’t leaving the same. A warm towel was placed on my shoulders like I just won an award for surviving Monday. The aromatherapy? Instant personality shift. I didn’t just relax — I decompressed like a forgotten soda can.”, “Honestly, I thought I was just getting a massage. What I got instead was emotional release, physical weightlessness, and unsolicited life perspective. I’d give it 6 stars if my fingers weren’t too relaxed to type.”
- [reviewAuthor]: A playful or human name, optionally with a funny tag to show transformation. Examples: Taylor F., Fully Rebooted Human; Jordan R., Soft as a Marshmallow; Lexi M., Unrecognizably Chill; Sam D., Probably Still in the Steam Room
- [subHeaderChild]: Section subheader for a child element (follow similar style as [subHeader])
- [mediumContentChild]: Brief paragraph for a child element (follow similar style as [mediumcontent])

Pay attention to:
- Each section's suggestion indicates its intended purpose/topic
- Keep content aligned with suggestion themes
- Maintain professional tone matching company's business type
- For [longContent], use [BREAK] to separate paragraphs
- Stay within word limits for each placeholder type
- For icons: use standard icon names from common libraries
- For buttons: consider the original button text when suggesting improvements
- For AI image prompts: include specific details about desired style, composition, and mood

Most Importantly : you should never miss a placeholder, if you do, you will be penalized
Generate content only for the passed Current sections. Do not generate random or unrelated content.


Json Response format:
{{
    "sections": [
        {{
            "identifier": "placeholder_X",
            "type": "type",
            "text": "generated content"
        }}
    ]
}}"""
    else:
        return f"""Generate content for website sections based on placeholders and section suggestions.

Input Sections: {{"sections":{json_content}}}
Company Info: {json.dumps(company_info)}

Guidelines for placeholders:
- [header]: Title of the section (max 8 words)
- [subHeader]: Section header/subtitle (max 12 words)
- [content]: Single paragraph content (max 70 words)
- [mediumcontent]: Brief paragraph (max 20 words)
- [shortContent]: Very brief text (max 5 words)
- [longContent]: Multiple paragraphs, use [BREAK] for separation
- [img]: Image keyword (a keyword for searching an image on Unsplash, one word only if the keywords would not be releavent consider use like the industry name to be sure we will have accurate image at least with same theme)
- [icon]: Name of a related icon from svgapi icon library (e.g., 'cart', 'user', 'phone')
- [button]: Improved button text suggestion based on original text and context (max 4 words)
- [ai_img]: Detailed prompt for AI image generation (20-30 words, describing style, mood, and content)
- [achievementNumber]: Quantifiable achievement or metric (e.g., "100% of guests leave more relaxed than when they arrived", "9 out of 10 hot stones say they're living their best life", "200+ sighs of bliss recorded daily", "95% cucumber slice retention rate"). Should be attention-grabbing and express success, excellence, or satisfaction.
- [achievementDesc]: Description of what the achievement reflects, typically in a lighthearted or promotional way (e.g., "Customers are floating in pure bliss", "Guests forget their stress, their worries — and sometimes their name", "Facial glows detected from three rooms away", "Every massage unlocks a little more inner peace (and sometimes old memories)"). Summarizes the result or emotional impact for the customer.
- [reviewTitle]: A short, catchy summary of the review. Examples: “I Transcended. In a Robe.”, “This Spa Unclenched My Soul”, “10/10 Would Melt Again”, “Officially Softer Than a Cloud”
- [reviewBodyShort]: A very short, punchy review (max 150 characters). Examples: “I came in stressed, left a puddle of joy.”, “The steam room gave me clarity and frizz-free hair.”, “I forgot what year it was, in the best way.”
- [reviewBodyMedium]: A balanced, expressive review with light humor or description (max 300 characters). Examples: “This spa didn’t just relax me — it turned me into a sleepy little blanket burrito. My muscles gave up and said thank you.”, “Was it the tea? The massage? The robe? I don’t know — but I left with zero tension and possibly new skin.”
- [reviewBodyLong]: A full-length, narrative-style review (max 800 characters). Examples: “From the moment I walked in, I knew I wasn’t leaving the same. A warm towel was placed on my shoulders like I just won an award for surviving Monday. The aromatherapy? Instant personality shift. I didn’t just relax — I decompressed like a forgotten soda can.”, “Honestly, I thought I was just getting a massage. What I got instead was emotional release, physical weightlessness, and unsolicited life perspective. I’d give it 6 stars if my fingers weren’t too relaxed to type.”
- [reviewAuthor]: A playful or human name, optionally with a funny tag to show transformation. Examples: Taylor F., Fully Rebooted Human; Jordan R., Soft as a Marshmallow; Lexi M., Unrecognizably Chill; Sam D., Probably Still in the Steam Room
- [subHeaderChild]: Section subheader for a child element (follow similar style as [subHeader])
- [mediumContentChild]: Brief paragraph for a child element (follow similar style as [mediumcontent])

Pay attention to:
- Use each section's suggestion to determine content theme/topic
- Generate content that reflects company profile
- Follow word limits for each placeholder type
- Keep content aligned with section suggestions
- For icons: use standard icon names from common libraries
- For buttons: consider the original button text when suggesting improvements
- For AI image prompts: include specific details about style, composition, and mood

Most Importantly : you should never miss a placeholder, if you do, you will be penalized

Json Response format:
{{
    "sections": [
        {{
            "identifier": "placeholder_X",
            "type": "type",
            "text": "generated content"
        }}
    ]
}}"""


from azure.storage.blob import BlobServiceClient
import json
from datetime import datetime
import os

async def main(req: func.HttpRequest) -> func.HttpResponse:
    try:
        req_body = req.get_json()




        # Create a unique blob name using timestamp
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S_%f")
        blob_name = f"debug_request_{timestamp}.json"

        # Get blob service client using connection string
        connection_string = os.environ["AzureWebJobsStorage"]
        blob_service_client = BlobServiceClient.from_connection_string(connection_string)
        
        # Get container client - create container if it doesn't exist
        container_client = blob_service_client.get_container_client("debug-updatesection")
        if not container_client.exists():
            container_client.create_container()
        
        # Convert request body to JSON string
        json_content = json.dumps(req_body, indent=2)
        
        # Upload to blob
        blob_client = container_client.get_blob_client(blob_name)
        blob_client.upload_blob(json_content, overwrite=True)



        sections = req_body.get('sections', [])
        company_info = {
            'companyName': req_body.get('companyName', ''),
            'companyEmail': req_body.get('companyEmail', ''),
            'companyPhone': req_body.get('companyPhone', ''),
            'companyAddress': req_body.get('companyAddress', ''),
            'description': req_body.get('description', ''),
            'businessType': req_body.get('businessType', ''),
            'sectionsToUpdate': req_body.get('sectionsToUpdate', []),
            'page_description': req_body.get('pageDescription', '')
        }

        openai_client = OpenAIClient(API_CONFIGS)
        updated_sections = await update_sections_with_openai(sections, company_info, openai_client)
        
        if updated_sections is None:
            return func.HttpResponse(
                "Failed to process sections with OpenAI.",
                status_code=500
            )
            
        return func.HttpResponse(
            json.dumps(updated_sections),
            status_code=200,
            mimetype="application/json"
        )
        
    except ValueError as e:
        return func.HttpResponse(
            f"Invalid JSON input: {str(e)}",
            status_code=400
        )
    except Exception as e:
        logging.error(f"Unexpected error: {str(e)}")
        return func.HttpResponse(
            f"An unexpected error occurred: {str(e)}",
            status_code=500
        )
