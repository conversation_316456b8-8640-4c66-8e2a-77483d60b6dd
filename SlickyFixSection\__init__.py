

import azure.functions as func
import json
import logging
from openai import OpenAI

client = OpenAI(
  
  api_key ='********************************************************',
)
# Define a dictionary to store the data
data_store = {
    'TEXT': [],
    'PRODUCT': []
}

def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Python HTTP trigger function processed a request.')

    try:
        # Get the request parameters
        doc_type = req.params.get('doctype')
        work_request = req.params.get('work')
        format_request = req.params.get('format')

        # Get the JSON data from the request body
        request_data = req.get_json()

        # Extract merchant information and product details
        merchant_info = request_data.get('merchant', {})
        product_info = request_data.get('product', {})
        text_content = request_data.get('text', '')

        # Merchant template JSON data
        merchant_template_json = [
            "businessLegalName", "doingBusinessAs", "name", "isFranchise", "franchiseName",
            "isHeadquarter", "email", "aboutUs", "contactName", "address1", "address2",
            "city", "state", "zip", "country", "fax", "businessPhone", "officePhone",
            "supportPhone", "administratorEmail", "fromEmail", "supportEmail", "note",
            "url", "domain", "customDomain", "companySize", "address", "selectedLanguages",
            "selectedBlocks", "selectedPlugins", "selectedModule", "selectedCountry",
            "selectedCurrency", "roles", "hours", "shipFroms", "shippingMethods",
            "shippingRates", "pickUpRates", "domains", "socialMedias", "slickyServices",
            "paymentMethods", "questions", "membershipName", "templateId", "status",
            "cellPhone"
        ]

        # Create a new JSON with only keys from the template
        filtered_merchant_json = {key: merchant_info[key] for key in merchant_template_json if key in merchant_info}
        filtered_product_json = product_info

        # Update the appropriate data store based on the document type
        if doc_type in data_store:
            if work_request == 'IMPROVE' or work_request == 'GENERATE':
                if doc_type == 'PRODUCT' or doc_type == 'TEXT':
                    if format_request == 'SHORT' or format_request == 'LONG':
                        alternatives = generate_alternatives(text_content,doc_type,work_request,format_request,filtered_merchant_json,filtered_product_json)

                        if alternatives:
                            return func.HttpResponse(json.dumps(alternatives), mimetype="application/json", status_code=200)
                        else:
                            return func.HttpResponse(json.dumps({'error': 'An Error Occurred, please try again.'}), mimetype="application/json", status_code=400)
                    else:
                        return func.HttpResponse(json.dumps({'error': 'Invalid format type.'}), mimetype="application/json", status_code=400)
                else:
                    return func.HttpResponse(json.dumps({'error': 'Invalid doc type.'}), mimetype="application/json", status_code=400)
            else:
                return func.HttpResponse(json.dumps({'error': 'Invalid work request.'}), mimetype="application/json", status_code=400)
        else:
            return func.HttpResponse(json.dumps({'error': 'Invalid document type.'}), mimetype="application/json", status_code=400)
    except Exception as e:
        return func.HttpResponse(json.dumps({'error': str(e)}), mimetype="application/json", status_code=500)


def generate_alternatives(text,doc_type,work_request,format_request,filtered_merchant_json,filtered_product_json):
    filtered_merchant_json_str = json.dumps(filtered_merchant_json)
    filtered_product_json_str = json.dumps(filtered_product_json)

    alternatives = []
    for _ in range(3):
        if (work_request == "IMPROVE"):
            prompt = """imrpove the input paragraphe and return an array with 3 json objects with one key text with the new generated text {{format_request_str}}
            NB1 :  respect the same input text lenth
            NB2: if you find any releavant information from provided merchant and product dicts if there provided try fill it in the generated outputs if it fits any slot
            NB3 :  don't make any real changes in the text , just fixe the spelling and improve it if you can in term of keywords and SEO and if there any value to fill from merchant and product data
            NB4 : use Merchant and product data only if there already mentionned in the input text but their wrong or need to be fixed
            NB5 : if input text is an html content , take care of formatting text with colors to have a better styled description
            input text : {{input_text}}
            Merchant Data : {{filtered_merchant_json_str}}
            Product Data : {{filtered_product_json_str}}
            Result :
            """

        else:

            prompt = """regenerate completly the input paragraphe taking releavent info from merchant or product data if needed and return an array with 3 json objects with one key text with the new generated text {{format_request_str}}
            NB :  generated text rether have same meaning as the input but enhance it in term of seo and text quality
            NB2 :  you can try more detailed text for some generations (more characters )
            NB3 : if input text is an html content , take care of formatting text with colors and headings because it will be placed in an HTML textarea (return an html code)
            NB4 : if you need any information about business use the merchant data or product data if needed only

            input text : {{input_text}}
            Merchant Data : {{filtered_merchant_json_str}}
            Product Data : {{filtered_product_json_str}}

            Result :
            """

        if(format_request == 'LONG' ):
            format_request_str = "(each text in the same size or longer than original text)"
        else:
            format_request_str = "(maximum 80 caracters lentgh per text)"


        iter = 0
        while (iter < 3):
            try:
                messages = [{"role":"user","content":prompt.replace("{{input_text}}",text).replace("{{filtered_merchant_json_str}}",str(filtered_merchant_json_str) ).replace("{{filtered_product_json_str}}",str(filtered_product_json_str) ).replace("{{format_request_str}}",str(format_request_str) ) }]
                result = client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=messages
                )
                alternatives = json.loads(result.choices[0].message.content)
                return alternatives
            except:
                iter +=1
                continue
    return None
