import json
import logging
from scipy import spatial
import azure.functions as func
from openai import OpenAI
#API from test3 metasence account
client = OpenAI(
  api_key ='********************************************************',
)

# Load the precomputed embeddings from the JSON file
with open('categories.json', 'r') as f:
    categories_data = json.load(f)

def get_embedding(text):
    response = client.embeddings.create(
        input=text,
        model="text-embedding-ada-002"
    )
    return response.data[0].embedding

def cosine_similarity(embedding1, embedding2):
    cos_sim = 1 - spatial.distance.cosine(embedding1, embedding2)
    return cos_sim

def find_nearest_category(description_embedding, categories_data):
    max_similarity = float('-inf')
    nearest_keyword = None
    parent_category = None
    test = []
    for keyword_info in categories_data:
        keyword = keyword_info['keyword']
        category_name = keyword_info['categoryName']
        keyword_embedding = keyword_info['embedding']
        similarity = cosine_similarity(description_embedding, keyword_embedding)

        test.append({"parent_category": category_name, "nearest_keyword": keyword, "similarity":str(similarity)})

        if similarity > max_similarity:
            max_similarity = similarity
            nearest_keyword = keyword
            parent_category = category_name
    
    return {"parent_category": parent_category, "nearest_keyword": nearest_keyword, "similarity":str(max_similarity), "similarities":str(test)}


def main(req: func.HttpRequest) -> func.HttpResponse:
    logging.info('Python HTTP trigger function processed a request.')

    description = req.params.get('description')
    debug = req.params.get('debug')
    if not description:
        try:
            req_body = req.get_json()
        except ValueError:
            pass
        else:
            description = req_body.get('description')

    if description:
        # Generate embedding for the description
        description_embedding = get_embedding(description)
        
        # Find the nearest category
        nearest_category = find_nearest_category(description_embedding, categories_data)
        if debug :
            return func.HttpResponse(json.dumps({"category":nearest_category["parent_category"], "nearest_keyword":nearest_category["nearest_keyword"] , "similarity":nearest_category["similarity"] , "similarities":nearest_category["similarities"] }), mimetype="application/json", status_code=200 )
        else:
            return func.HttpResponse(json.dumps({"category":nearest_category["parent_category"]}), mimetype="application/json", status_code=200 )

    else:
        return func.HttpResponse(
            "This HTTP triggered function executed successfully. Pass a description in the query string or in the request body for a personalized response.",
            status_code=200
        )